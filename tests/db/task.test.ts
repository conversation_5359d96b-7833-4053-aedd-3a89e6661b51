import {
  addTask,
  bindRemoteTask,
  getTaskByRemoteTaskId,
  getTasksByPrjId,
  recordTaskError,
  resetTaskStateToStart,
  updateTaskRemoteStatus,
} from "@/lib/domain/task/dbt";
import {
  TaskRemoteInnerState,
  type TaskReq,
  TaskType,
} from "@/lib/domain/task/svc";
import { getTestPrisma } from "@/tests/db/db-fixture";
import { PrismaClient, Project } from "@prisma/client";
import { beforeEach, describe, expect, it, vi } from "vitest";

const operator = {
  id: "userId",
  isTestAccount: false,
};

describe("getTasksByPrjId", () => {
  let mockPrj: Project;
  let prisma: PrismaClient;

  beforeEach(async () => {
    prisma = await getTestPrisma();

    const otherPrj = await prisma.project.create({
      data: {
        id: "other-prjId",
        name: "other-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });
    mockPrj = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });

    await prisma.task.createMany({
      data: [
        {
          id: "uuid1",
          project_id: otherPrj.id,
          task_type: TaskType.ingest,
          task_args: { input: "other-project-data" },
          remote_task_id: "other-remote-1",
          remote_task_status: TaskRemoteInnerState.NIL,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T10:00:00Z"),
        },
        {
          id: "uuid2",
          project_id: mockPrj.id,
          task_type: TaskType.ingest,
          task_args: { source: "dataset1.zip", format: "dicom" },
          remote_task_id: "remote-1",
          remote_task_status: TaskRemoteInnerState.NIL,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T09:00:00Z"),
        },
        {
          id: "uuid3",
          project_id: mockPrj.id,
          task_type: TaskType.save_as,
          task_args: { min_val: 0, max_val: 255 },
          remote_task_id: "remote-2",
          remote_task_status: TaskRemoteInnerState.PENDING_ARGS_AVAIL,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T11:00:00Z"),
        },
        {
          id: "uuid4",
          project_id: mockPrj.id,
          task_type: TaskType.register,
          task_args: { method: "rigid", metric: "mi" },
          remote_task_id: "remote-3",
          remote_task_status: TaskRemoteInnerState.PENDING_NODE_ASSIGNMENT,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T12:00:00Z"),
        },
        {
          id: "uuid5",
          project_id: mockPrj.id,
          task_type: TaskType.save_as,
          task_args: { format: "nifti", compression: true },
          remote_task_id: "remote-4",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T06:00:00Z"),
          finished_at: new Date("2025-01-01T06:30:00Z"),
        },
        {
          id: "uuid6",
          project_id: mockPrj.id,
          task_type: TaskType.save_as,
          task_args: { name: "processed_data", location: "/exports" },
          remote_task_id: "remote-5",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T07:00:00Z"),
          finished_at: new Date("2025-01-01T07:30:00Z"),
        },
        {
          id: "uuid7",
          project_id: mockPrj.id,
          task_type: TaskType.save_as,
          task_args: { lower: 10, upper: 90 },
          remote_task_id: "remote-6",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T08:00:00Z"),
          finished_at: new Date("2025-01-01T08:45:00Z"),
        },
        {
          id: "uuid8",
          project_id: mockPrj.id,
          task_type: TaskType.register,
          task_args: { method: "affine" },
          remote_task_id: "remote-7",
          remote_task_status: TaskRemoteInnerState.FAILED,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T05:00:00Z"),
          finished_at: new Date("2025-01-01T05:15:00Z"),
          error: "Registration failed: insufficient contrast",
        },
        {
          id: "uuid9",
          project_id: mockPrj.id,
          task_type: TaskType.ingest,
          task_args: { source: "corrupted.zip" },
          remote_task_id: "remote-8",
          remote_task_status: TaskRemoteInnerState.FAILED,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T04:00:00Z"),
          finished_at: new Date("2025-01-01T04:10:00Z"),
          error: "File corruption detected",
        },
        {
          id: "uuid10",
          project_id: mockPrj.id,
          task_type: TaskType.save_as,
          task_args: { threshold: 0.5 },
          remote_task_id: null,
          remote_task_status: null,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T03:00:00Z"),
        },
        {
          id: "uuid11",
          project_id: mockPrj.id,
          task_type: TaskType.save_as,
          task_args: { format: "png" },
          remote_task_id: "remote-9",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T07:00:00Z"),
          finished_at: new Date("2025-01-01T07:20:00Z"),
        },
        {
          id: "uuid12",
          project_id: mockPrj.id,
          task_type: TaskType.merge_channels,
          task_args: { auto_scale: true },
          remote_task_id: "remote-10",
          remote_task_status: TaskRemoteInnerState.PENDING_ARGS_AVAIL,
          created_by_id: operator.id,
          start_at: new Date("2025-01-01T13:00:00Z"),
        },
      ],
    });
  });

  it("should filter tasks by project id", async () => {
    const tasks = await getTasksByPrjId(prisma, {
      prjId: "other-prjId",
    });
    expect(tasks).toHaveLength(1);
    expect(tasks[0].id).toBe("uuid1");
  });

  it("should filter tasks by status", async () => {
    const [failedTasks, finishedTasks, runningTasks] = await Promise.all([
      getTasksByPrjId(prisma, {
        prjId: mockPrj.id,
        remoteStatus: TaskRemoteInnerState.FAILED,
      }),
      getTasksByPrjId(prisma, {
        prjId: mockPrj.id,
        remoteStatus: TaskRemoteInnerState.FINISHED,
      }),
      getTasksByPrjId(prisma, {
        prjId: mockPrj.id,
        remoteStatus: { not: null },
        finishedAt: { not: null },
      }),
    ]);

    expect([
      runningTasks.length,
      finishedTasks.length,
      failedTasks.length,
    ]).toEqual([6, 4, 2]);

    expect(
      failedTasks.every(
        (task) =>
          task.remote_task_status === TaskRemoteInnerState.FAILED &&
          task.finished_at !== null
      )
    ).toBe(true);

    expect(
      finishedTasks.every(
        (task) => task.remote_task_status === TaskRemoteInnerState.FINISHED
      )
    ).toBe(true);

    expect(runningTasks.every((task) => task.finished_at !== null)).toBe(true);
  });

  it("should sort and limit correctly", async () => {
    const byStartAt = await getTasksByPrjId(prisma, {
      prjId: mockPrj.id,
      orderBy: { start_at: "desc" },
      length: 4,
    });

    expect(byStartAt.length).toBe(4);

    expect(byStartAt.map((task) => task.id)).toEqual([
      "uuid12",
      "uuid4",
      "uuid3",
      "uuid2",
    ]);
  });
});

describe("resetTaskStateToStart", () => {
  it("should reset task state to start", async () => {
    const prisma = await getTestPrisma();
    const project = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });
    const task = await prisma.task.create({
      data: {
        project_id: project.id,
        task_type: TaskType.ingest,
        task_args: {},
        start_at: new Date("2025-01-01T10:00:00Z"),
        created_by_id: operator.id,
      },
    });

    const startAt = new Date("2025-05-08 10:45:15:03");
    vi.setSystemTime(startAt);

    await resetTaskStateToStart(prisma, {
      task_id: task.id,
      operator,
    });

    const updatedTask = await prisma.task.findUnique({
      where: {
        id: task.id,
      },
    });

    expect(updatedTask).toEqual({
      id: task.id,
      project_id: project.id,
      task_type: TaskType.ingest,
      task_args: {},
      remote_task_id: null,
      remote_task_status: null,
      start_at: startAt,
      finished_at: null,
      error: null,
      created_by_id: operator.id,
      extra: null,
    });
  });
});

describe("recordTaskError", () => {
  it("should record task error", async () => {
    const prisma = await getTestPrisma();
    const project = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });
    const startAt = new Date("2025-01-01T10:00:00Z");

    const task = await prisma.task.create({
      data: {
        project_id: project.id,
        task_type: TaskType.ingest,
        task_args: {},
        start_at: startAt,
        created_by_id: operator.id,
        remote_task_id: "remote-task-id",
      },
    });

    const finishedAt = new Date("2025-05-08 10:45:15:03");
    vi.setSystemTime(finishedAt);

    await recordTaskError(prisma, {
      task_id: task.id,
      errorMsg: "test error",
      remote_task_status: TaskRemoteInnerState.FAILED,
    });

    const updatedTask = await prisma.task.findUnique({
      where: {
        id: task.id,
      },
    });

    expect(updatedTask).toEqual({
      id: task.id,
      project_id: project.id,
      task_type: TaskType.ingest,
      task_args: {},
      remote_task_id: "remote-task-id",
      remote_task_status: TaskRemoteInnerState.FAILED,
      start_at: startAt,
      finished_at: finishedAt,
      error: "test error",
      created_by_id: operator.id,
      extra: null,
    });
  });
});

describe("bindRemoteTask", () => {
  it("should bind remote task", async () => {
    const prisma = await getTestPrisma();
    const project = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });

    const startAt = new Date("2025-01-01T10:00:00Z");
    const task = await prisma.task.create({
      data: {
        project_id: project.id,
        task_type: TaskType.ingest,
        task_args: {},
        created_by_id: operator.id,
        start_at: startAt,
      },
    });

    await bindRemoteTask(prisma, {
      task_id: task.id,
      remote_task_id: "remote-task-id",
      extra: {
        decodedItemId: "item-id",
      },
    });

    const updatedTask = await prisma.task.findUnique({
      where: { id: task.id },
    });

    expect(updatedTask).toEqual({
      id: task.id,
      project_id: project.id,
      task_type: TaskType.ingest,
      task_args: {},
      remote_task_id: "remote-task-id",
      remote_task_status: null,
      start_at: startAt,
      finished_at: null,
      error: null,
      created_by_id: operator.id,
      extra: { decodedItemId: "item-id" },
    });
  });
});

describe("addTask", () => {
  it("should add task", async () => {
    const prisma = await getTestPrisma();
    const project = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });

    const taskId = "unique-id-25";

    const taskReq: TaskReq = {
      taskType: TaskType.ingest,
      taskArgs: {
        projectId: project.id,
        sourcePath: "source-path",
        outputFolderId: "output-folder-id",
        outputName: "output-name",
      },
    };

    await addTask(prisma, {
      projectId: project.id,
      taskReq: taskReq,
      operator,
      taskId: taskId,
    });

    const updatedTask = await prisma.task.findUnique({
      where: { id: taskId },
    });

    expect(updatedTask).toMatchObject({
      id: taskId,
      project_id: project.id,
      task_type: taskReq.taskType,
      task_args: taskReq.taskArgs,
      remote_task_id: null,
      remote_task_status: null,
      finished_at: null,
      error: null,
      created_by_id: operator.id,
      extra: null,
    });
  });
});

describe("updateTaskRemoteStatus", () => {
  it("should update task remote status", async () => {
    const prisma = await getTestPrisma();
    const project = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });

    const task = await prisma.task.create({
      data: {
        project_id: project.id,
        task_type: TaskType.ingest,
        task_args: {},
        created_by_id: operator.id,
      },
    });

    await updateTaskRemoteStatus(prisma, {
      taskId: task.id,
      remoteTaskStatus: TaskRemoteInnerState.PENDING_ARGS_AVAIL,
    });

    const updatedTask = await prisma.task.findUnique({
      where: { id: task.id },
    });

    expect(updatedTask?.remote_task_status).toBe(
      TaskRemoteInnerState.PENDING_ARGS_AVAIL
    );
  });
});

describe("getTaskByRemoteTaskId", () => {
  it("should get task by remote task id", async () => {
    const prisma = await getTestPrisma();
    const project = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });

    const task = await prisma.task.create({
      data: {
        project_id: project.id,
        task_type: TaskType.ingest,
        task_args: {},
        created_by_id: operator.id,
        remote_task_id: "remote-task-id",
      },
    });

    const queriedTask = await getTaskByRemoteTaskId(prisma, {
      remoteTaskId: "remote-task-id",
      taskId: task.id,
    });

    expect(queriedTask!.id).toEqual(task.id);
  });
});
