"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import {
  AddTaskInputSchema,
  RerunTaskInputSchema,
} from "@/lib/domain/task/svc";
import z from "zod";

const AddTaskOutputSchema = z.object({
  taskId: z.string(),
});

export const addTask = authActionClient
  .inputSchema(AddTaskInputSchema)
  .outputSchema(AddTaskOutputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const taskSvc = await di.getTaskSvc();
    const task = await taskSvc.add_task_and_run(operator, parsedInput);
    return { taskId: task.id, test: 1 };
  });

export const rerunTask = authActionClient
  .inputSchema(RerunTaskInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const taskSvc = await di.getTaskSvc();
    await taskSvc.rerunTaskById(operator, parsedInput);
  });
