import {
  getStepSettingFormInfo as getSaveAsStepSettingFormInfo,
  getStepOutputFormInfo,
  OutputFormNextTrigger,
  useGetSettingStore as useGetSaveAsSettingStore,
  type OutputFormFields as SaveAsOutputFormFields,
  type Setting<PERSON><PERSON>Fields as SaveAsSettingFormFields,
  type SaveAsStore,
} from "@/app-ui/components/image/operation/save-as";
import { getTaskResultStepGen } from "@/app-ui/components/image/operation/task-result-step";
import { FormItemInfo, IrsFormInfo } from "@/app-ui/components/utils/form";
import { ModalTrigger } from "@/app-ui/components/utils/modal-trigger";
import {
  createFormStepGen,
  IrsSteps,
  NextStepBtn,
  type StepFormUtils,
} from "@/app-ui/components/utils/step-form";
import { useStateMergedChannels } from "@/lib/app-store/image-slice";
import { HelpLinks } from "@/lib/domain/common/constants";
import type { ImgInfo } from "@/lib/domain/img/query/svc";
import { TaskType } from "@/lib/domain/task/svc";
import IconApply from "@/public/<EMAIL>";
import IconQuestionMark from "@/public/<EMAIL>";
import { Button, Flex, Radio, Space, Tooltip, Typography } from "antd";
import Image from "next/image";
import Link from "next/link";
import { type ReactNode } from "react";

type ApplyStrategy = "customize" | "original";

type OutputFormFields = SaveAsOutputFormFields & {};

type SettingFormFields = SaveAsSettingFormFields & {
  apply_strategy: ApplyStrategy;
};

type SettingStore = SettingFormFields & {
  selectedChannels: {
    id: number;
    name: string;
    rescale_range: {
      min: number;
      max: number;
    };
  }[];
};

function getSettingStepFormInfo({ imgName }: { imgName: string }) {
  const saveAsStepFormInfo = getSaveAsStepSettingFormInfo<SettingFormFields>({
    imgName: imgName,
  });

  const formItems: FormItemInfo<SettingFormFields>[] = [
    ...saveAsStepFormInfo.formItems,
    {
      nameForLayout: "strength_desc",
      formItemProps: {
        children: (
          <Typography.Text type="secondary">
            将 Apply strategy 选择的强度区间，映射至图像位深的整个区间
          </Typography.Text>
        ),
      },
    },
    {
      name: "apply_strategy",
      label: {
        name: "Apply strategy",
        desc: "变换策略",
      },
      formItemProps: {
        children: (
          <Radio.Group>
            <Space direction="vertical">
              <Space direction="vertical">
                <Radio value={"customize"}>Customize contrast</Radio>
                <Typography.Text type="secondary">
                  使用自定义 Min & Max 强度值进行映射变换操作
                </Typography.Text>
              </Space>
              <Space direction="vertical">
                <Radio value={"original"}>Original contrast</Radio>
                <Typography.Text type="secondary">
                  使用原始 Min & Max 强度值进行映射变换操作
                </Typography.Text>
              </Space>
            </Space>
          </Radio.Group>
        ),
      },
    },
  ];

  const stepFormInfo: IrsFormInfo<SettingFormFields> = {
    ...saveAsStepFormInfo,
    formItems: formItems,
    formProps: {
      ...saveAsStepFormInfo.formProps,
      initialValues: {
        ...saveAsStepFormInfo.formProps?.initialValues,
        apply_strategy: "customize",
      },
    },
  };

  return stepFormInfo;
}

function SettingFormNextTrigger({
  imgInfo,
  stepFormUtils,
  stepIdx,
}: {
  imgInfo: ImgInfo;
  stepFormUtils: StepFormUtils<SaveAsStore<SettingStore>>;
  stepIdx: number;
}) {
  const channelMetas = imgInfo.imgMeta.channels;
  const channelViews = useStateMergedChannels(channelMetas);

  const getSaveAsSettingStore = useGetSaveAsSettingStore<SettingFormFields>({
    imgInfo: imgInfo,
  });
  const saveStore = async () => {
    const form = stepFormUtils.getForm<SettingFormFields>(stepIdx);
    const settingStore = await getSaveAsSettingStore(form);

    if (!settingStore) {
      return;
    }

    const { selectedChannels, apply_strategy } = settingStore;

    const channels = selectedChannels.map((x) => {
      let min: number, max: number;
      switch (apply_strategy) {
        case "customize":
          const viewCh = channelViews.find((x) => x.id === x.id)!;
          min = viewCh.min;
          max = viewCh.max;
          break;
        case "original":
          const metaCh = channelMetas.find((x) => x.id === x.id)!;
          min = metaCh.min;
          max = metaCh.max;
          break;
        default:
          throw new Error(`${apply_strategy} not supported`);
      }
      return {
        id: x.id,
        name: x.name,
        rescale_range: { min, max },
      };
    });

    stepFormUtils.storeRef.current.setting = {
      ...settingStore,
      selectedChannels: channels,
    };
  };

  return (
    <NextStepBtn
      stepIdx={stepIdx}
      stepUtils={stepFormUtils}
      onNext={saveStore}
    />
  );
}

function getSettingStepGen({ imgInfo }: { imgInfo: ImgInfo }) {
  const stepFormInfo = getSettingStepFormInfo({ imgName: imgInfo.name });

  const stepGen = createFormStepGen<
    SettingFormFields,
    SaveAsStore<SettingStore>
  >({
    title: "参数设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return <SettingFormNextTrigger {...options} imgInfo={imgInfo} />;
    },
  });

  return stepGen;
}

function getOutputStepGen({
  imgName,
  projectId,
}: {
  imgName: string;
  projectId: string;
}) {
  const stepFormInfo = (utils: StepFormUtils<SaveAsStore<SettingStore>>) =>
    getStepOutputFormInfo({
      imgName: imgName,
      projectId: projectId,
      setting: utils.storeRef.current.setting,
    });

  const stepGen = createFormStepGen<
    OutputFormFields,
    SaveAsStore<SettingStore>
  >({
    title: "输出设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return (
        <OutputFormNextTrigger
          {...options}
          projectId={projectId}
          taskType={TaskType.rescale_intensity}
        />
      );
    },
  });

  return stepGen;
}

function ApplySteps({ imgInfo }: { imgInfo: ImgInfo }) {
  const { name: imgName, prjId: projectId } = imgInfo;

  const settingStepGen = getSettingStepGen({ imgInfo: imgInfo });

  const outputStepGen = getOutputStepGen({
    imgName: imgName,
    projectId: projectId,
  });

  const resultStepGen = getTaskResultStepGen({ projectId: projectId });

  const stepGens = [settingStepGen, outputStepGen, resultStepGen];

  return <IrsSteps stepGens={stepGens} />;
}

function ApplyModalTrigger({
  children,
  imgInfo,
}: {
  children: ReactNode;
  imgInfo: ImgInfo;
}) {
  return (
    <ModalTrigger
      modalProps={{
        title: "Apply",
        children: <ApplySteps imgInfo={imgInfo} />,
      }}
      trigger={children}
    />
  );
}

export function ApplyTransIcon({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <Flex vertical align="center" justify="center">
      <ApplyModalTrigger imgInfo={imgInfo}>
        <Button
          color="default"
          variant="text"
          style={{ width: 54, height: 54 }}
          className="group content-center relative p-0"
        >
          <Image src={IconApply} alt="saturate" className="m-auto" />
          <Tooltip
            color="white"
            title={
              <Link
                href={HelpLinks.ImageOps}
                target="_blank"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                将当前设置强度区间，映射至图像位深的整个区间
              </Link>
            }
          >
            <Image
              src={IconQuestionMark}
              alt="question"
              className="absolute top-0 right-0 invisible group-hover:visible"
            />
          </Tooltip>
        </Button>
      </ApplyModalTrigger>

      <span className="text-wrap text-xs text-center" style={{ width: 54 }}>
        Apply
      </span>
    </Flex>
  );
}
