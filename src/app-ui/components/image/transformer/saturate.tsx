import {
  getStepSettingFormInfo as getSaveAsStepSettingFormInfo,
  getStepOutputFormInfo,
  OutputFormNextTrigger,
  useGetSettingStore as useGetSaveAsSettingStore,
  type OutputFormFields as SaveAsOutputFormFields,
  type SettingF<PERSON>Fields as SaveAsSettingFormFields,
  type SaveAsStore,
} from "@/app-ui/components/image/operation/save-as";
import { getTaskResultStepGen } from "@/app-ui/components/image/operation/task-result-step";
import { ModalTrigger } from "@/app-ui/components/utils/modal-trigger";
import {
  createFormStepGen,
  IrsSteps,
  NextStepBtn,
  type StepFormUtils,
} from "@/app-ui/components/utils/step-form";
import { useStateMergedChannels } from "@/lib/app-store/image-slice";
import { HelpLinks } from "@/lib/domain/common/constants";
import type { ImgInfo } from "@/lib/domain/img/query/svc";
import { TaskType } from "@/lib/domain/task/svc";
import IconQuestionMark from "@/public/<EMAIL>";
import IconSaturate from "@/public/<EMAIL>";
import { Button, Flex, Tooltip } from "antd";
import Image from "next/image";
import Link from "next/link";
import { type ReactNode } from "react";

type OutputFormFields = SaveAsOutputFormFields & {};

type SettingFormFields = SaveAsSettingFormFields & {};

type SettingStore = SettingFormFields & {
  selectedChannels: {
    id: number;
    name: string;
    clip_range: {
      min: number;
      max: number;
    };
  }[];
};

function SettingFormNextTrigger({
  imgInfo,
  stepFormUtils,
  stepIdx,
}: {
  imgInfo: ImgInfo;
  stepFormUtils: StepFormUtils<SaveAsStore<SettingStore>>;
  stepIdx: number;
}) {
  const channelMetas = imgInfo.imgMeta.channels;
  const channelViews = useStateMergedChannels(channelMetas);

  const getSaveAsSettingStore = useGetSaveAsSettingStore<SettingFormFields>({
    imgInfo: imgInfo,
  });
  const saveStore = async () => {
    const form = stepFormUtils.getForm<SettingFormFields>(stepIdx);
    const settingStore = await getSaveAsSettingStore(form);

    if (!settingStore) {
      return;
    }

    const { selectedChannels } = settingStore;

    const channels = selectedChannels.map((x) => {
      const viewCh = channelViews.find((x) => x.id === x.id)!;
      return {
        id: x.id,
        name: x.name,
        clip_range: {
          min: viewCh.min,
          max: viewCh.max,
        },
      };
    });

    stepFormUtils.storeRef.current.setting = {
      ...settingStore,
      selectedChannels: channels,
    };
  };

  return (
    <NextStepBtn
      stepIdx={stepIdx}
      stepUtils={stepFormUtils}
      onNext={saveStore}
    />
  );
}

function getSettingStepGen({ imgInfo }: { imgInfo: ImgInfo }) {
  const stepFormInfo = getSaveAsStepSettingFormInfo({ imgName: imgInfo.name });

  const stepGen = createFormStepGen<
    SettingFormFields,
    SaveAsStore<SettingStore>
  >({
    title: "参数设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return <SettingFormNextTrigger {...options} imgInfo={imgInfo} />;
    },
  });

  return stepGen;
}

function getOutputStepGen({
  imgName,
  projectId,
}: {
  imgName: string;
  projectId: string;
}) {
  const stepFormInfo = (utils: StepFormUtils<SaveAsStore<SettingStore>>) =>
    getStepOutputFormInfo({
      imgName: imgName,
      projectId: projectId,
      setting: utils.storeRef.current.setting,
    });

  const stepGen = createFormStepGen<
    OutputFormFields,
    SaveAsStore<SettingStore>
  >({
    title: "输出设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return (
        <OutputFormNextTrigger
          {...options}
          projectId={projectId}
          taskType={TaskType.clip_intensity}
        />
      );
    },
  });

  return stepGen;
}

function SaturateSteps({ imgInfo }: { imgInfo: ImgInfo }) {
  const { name: imgName, prjId: projectId } = imgInfo;

  const settingStepGen = getSettingStepGen({ imgInfo: imgInfo });

  const outputStepGen = getOutputStepGen({
    imgName: imgName,
    projectId: projectId,
  });

  const resultStepGen = getTaskResultStepGen({ projectId: projectId });

  const stepGens = [settingStepGen, outputStepGen, resultStepGen];

  return <IrsSteps stepGens={stepGens} />;
}

function SaturateModalTrigger({
  children,
  imgInfo,
}: {
  children: ReactNode;
  imgInfo: ImgInfo;
}) {
  return (
    <ModalTrigger
      modalProps={{
        title: "Saturate",
        children: <SaturateSteps imgInfo={imgInfo} />,
      }}
      trigger={children}
    />
  );
}

export function SaturateTransIcon({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <Flex vertical align="center" justify="center">
      <SaturateModalTrigger imgInfo={imgInfo}>
        <Button
          color="default"
          variant="text"
          style={{ width: 54, height: 54 }}
          className="group content-center relative p-0"
        >
          <Image src={IconSaturate} alt="saturate" className="m-auto" />
          <Tooltip
            color="white"
            title={
              <Link
                href={HelpLinks.ImageOps}
                target="_blank"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                按照通道 Min & Max 值，饱和最暗/最亮区间像素
              </Link>
            }
          >
            <Image
              src={IconQuestionMark}
              alt="question"
              className="absolute top-0 right-0 invisible group-hover:visible"
            />
          </Tooltip>
        </Button>
      </SaturateModalTrigger>

      <span className="text-wrap text-xs text-center" style={{ width: 54 }}>
        Saturate
      </span>
    </Flex>
  );
}
