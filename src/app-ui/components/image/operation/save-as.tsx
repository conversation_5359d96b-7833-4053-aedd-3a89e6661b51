"use client";

import { addTask } from "@/app-ui/actions/safe-actions/task";
import { DiskTreeSelector } from "@/app-ui/components/disk/folder-selector";
import { getTaskResultStepGen } from "@/app-ui/components/image/operation/task-result-step";
import { FolderSelector } from "@/app-ui/components/item/folder-selector";
import {
  FormItemInfo,
  FormSelect,
  IrsFormInfo,
} from "@/app-ui/components/utils/form";
import { ModalTrigger } from "@/app-ui/components/utils/modal-trigger";
import {
  IrsSteps,
  NextStepBtn,
  StepFormUtils,
  createFormStepGen,
} from "@/app-ui/components/utils/step-form";
import {
  SaveAsType,
  SupportedSaveAsType,
  XYRegion,
} from "@/lib/apis/diverse/image-endpoints";
import {
  selectDrawFeaturesBBox,
  useStateShownChannels,
  type BBox,
} from "@/lib/app-store/image-slice";
import { useAppSelector } from "@/lib/app-store/store";
import type { ImgInfo } from "@/lib/domain/img/query/svc";
import { TaskType } from "@/lib/domain/task/svc";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import IconSaveAs from "@/public/<EMAIL>";
import { snapdom } from "@zumer/snapdom";
import {
  Button,
  Checkbox,
  Flex,
  Input,
  Typography,
  type FormInstance,
} from "antd";
import Image from "next/image";
import { ReactNode } from "react";

type ChannelChosenType = "all" | "shown";

export type SettingFormFields = {
  channelChosenType: ChannelChosenType;
  splitChannel: boolean;
  saveAsType: SaveAsType;
};

export type OutputFormFields = {
  folder_name: string;
  tgt_folder_id?: string;
};

type SettingStore = SettingFormFields & {
  selectedChannels: { id: number; name: string }[];
  region?: XYRegion;
};

type OutputStore = OutputFormFields;

export type SaveAsStore<Setting = SettingStore, Output = OutputStore> = {
  setting: Setting;
  output: Output;
  taskId: string;
};

function RegionDesc({ imgName }: { imgName: string }) {
  const featureBBox = useAppSelector(selectDrawFeaturesBBox);
  const description = featureBBox ? JSON.stringify(featureBBox) : imgName;

  return (
    <>
      <Typography.Text ellipsis={{ tooltip: true }}>
        {description}
      </Typography.Text>
    </>
  );
}

export function getStepSettingFormInfo<Fields extends SettingFormFields>({
  imgName,
}: {
  imgName: string;
}): IrsFormInfo<Fields> {
  const formItems: FormItemInfo<SettingFormFields>[] = [
    {
      nameForLayout: "srcName",
      label: {
        name: "Object",
        desc: "对象",
      },
      formItemProps: {
        children: <RegionDesc imgName={imgName} />,
      },
    },
    {
      name: "channelChosenType",
      label: { name: "Channel", desc: "保存通道" },
      formItemProps: {
        children: (
          <FormSelect
            options={[
              {
                value: "shown",
                label: "Selected",
                desc: "应用于勾选通道",
              },
              {
                value: "all",
                label: "All",
                desc: "应用于图像所有通道",
              },
            ]}
          />
        ),
      },
    },
    {
      name: "splitChannel",
      label: { name: "Split dimension", desc: "需拆分后单独保存的维度" },
      formItemProps: {
        children: <Checkbox>Channel</Checkbox>,
        valuePropName: "checked",
      },
    },
    {
      name: "saveAsType",
      label: { name: "Target format type", desc: "目标图像格式类型" },
      formItemProps: {
        children: (
          <FormSelect
            options={SupportedSaveAsType.map((x) => {
              return { value: x };
            })}
          />
        ),
      },
    },
  ];

  const stepFormInfo: IrsFormInfo<SettingFormFields> = {
    formItems: formItems,
    formProps: {
      initialValues: {
        channelChosenType: "shown",
        splitChannel: false,
        saveAsType: "irs",
      },
      labelCol: { flex: "0 0 180px" },
      wrapperCol: { flex: "1 0" },
    },
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return stepFormInfo as IrsFormInfo<any>;
}

export function useGetSettingStore<Fields extends SettingFormFields>({
  imgInfo,
}: {
  imgInfo: ImgInfo;
}) {
  const channelMetas = imgInfo.imgMeta.channels;
  const shownChannels = useStateShownChannels(channelMetas);
  const featureBBox = useAppSelector(selectDrawFeaturesBBox);

  const getSettingStore = async (form: FormInstance<Fields>) => {
    const formValues = await form.validateFields();
    const channelChoosenType = formValues.channelChosenType;
    let selectedChannels = channelMetas.map((x) => ({
      id: x.id,
      name: x.name,
    }));

    if (channelChoosenType == "shown") {
      selectedChannels = shownChannels.map((x) => ({
        id: x.id,
        name: x.name,
      }));
    }

    if (selectedChannels.length === 0) {
      (form as unknown as FormInstance<SettingFormFields>).setFields([
        { name: "channelChosenType", errors: ["没有选中的通道"] },
      ]);
      return;
    }

    const region = bboxToRegion(featureBBox);

    return { ...formValues, selectedChannels, region };

    function bboxToRegion(bbox?: BBox) {
      if (bbox) {
        return {
          x: bbox.x,
          y: bbox.y,
          width: bbox.w,
          height: bbox.h,
        } as XYRegion;
      }
    }
  };

  return getSettingStore;
}

export function SettingFormNextTrigger<Fields extends SettingFormFields>({
  imgInfo,
  stepFormUtils,
  stepIdx,
}: {
  imgInfo: ImgInfo;
  stepFormUtils: StepFormUtils<SaveAsStore>;
  stepIdx: number;
}) {
  const getSettingStore = useGetSettingStore<Fields>({ imgInfo: imgInfo });
  const saveStore = async () => {
    const form = stepFormUtils.getForm<Fields>(stepIdx);
    const settingStore = await getSettingStore(form);
    stepFormUtils.storeRef.current.setting = settingStore;
  };

  return (
    <NextStepBtn
      stepIdx={stepIdx}
      stepUtils={stepFormUtils}
      onNext={saveStore}
    />
  );
}

function getSettingStepGen({ imgInfo }: { imgInfo: ImgInfo }) {
  const stepFormInfo = getStepSettingFormInfo({
    imgName: imgInfo.name,
  });

  const stepGen = createFormStepGen<SettingFormFields, SaveAsStore>({
    title: "参数设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return <SettingFormNextTrigger {...options} imgInfo={imgInfo} />;
    },
  });

  return stepGen;
}

export function getStepOutputFormInfo<
  Fields extends OutputFormFields,
  Setting extends SettingStore
>({
  projectId,
  imgName,
  setting,
}: {
  projectId: string;
  imgName: string;
  setting?: Setting;
}): IrsFormInfo<Fields> {
  const targetInDisk = setting?.saveAsType !== "irs";
  const stepFormInfo: IrsFormInfo<OutputFormFields> = {
    formItems: [
      {
        name: "folder_name",
        label: {
          name: "名称",
        },
        formItemProps: {
          children: <Input maxLength={30} showCount />,
          rules: [{ required: true, message: "请输入新名称", max: 30 }],
        },
      },
      {
        name: "tgt_folder_id",
        label: { name: "目标位置" },
        formItemProps: {
          required: true,
          children: (
            <>
              {targetInDisk ? (
                <DiskTreeSelector
                  isDir
                  projectId={projectId}
                  treeId="folder-selector"
                  placeholder={"默认云盘根目录"}
                />
              ) : (
                <FolderSelector
                  placeholder={"默认原图所在目录"}
                  prjId={projectId}
                />
              )}
            </>
          ),
          rules: [{ required: false }],
        },
      },
    ],
    formProps: {
      initialValues: {
        folder_name: imgName.replace(/\.[^/.]+$/, ""),
      },
      labelCol: { flex: "0 0 180px" },
      wrapperCol: { flex: "1 0" },
    },
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return stepFormInfo as IrsFormInfo<any>;
}

export function OutputFormNextTrigger<Fields extends OutputFormFields>({
  projectId,
  stepFormUtils,
  stepIdx,
  taskType,
}: {
  projectId: string;
  stepFormUtils: StepFormUtils<SaveAsStore>;
  stepIdx: number;
  taskType: TaskType;
}) {
  const handleApiErrorDefault = useHandleApiErrorDefault();

  const onNext = async () => {
    const form = stepFormUtils.getForm<Fields>(stepIdx);
    const formValues = await form.validateFields();
    stepFormUtils.storeRef.current.output = formValues;
    const stepsDom = stepFormUtils.stepsDomRef.current;

    if (!stepsDom) {
      throw new Error("steps dom is null");
    }
    const snapShot = await snapdom(stepsDom);
    const snapShotBlob = await snapShot.toBlob();
    const res = await addTask({
      projectId: projectId,
      taskReq: {
        taskType: taskType,
        taskArgs: stepFormUtils.storeRef.current,
      },
      snapShot: snapShotBlob,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiErrorDefault);

    const taskId = res.data?.taskId;
    stepFormUtils.storeRef.current.taskId = taskId;
  };

  return (
    <NextStepBtn stepIdx={stepIdx} stepUtils={stepFormUtils} onNext={onNext} />
  );
}

function getOutputStepGen({
  imgName,
  projectId,
}: {
  imgName: string;
  projectId: string;
}) {
  const stepFormInfo = (utils: StepFormUtils<SaveAsStore>) =>
    getStepOutputFormInfo({
      projectId: projectId,
      imgName: imgName,
      setting: utils.storeRef.current.setting,
    });

  const stepGen = createFormStepGen<OutputFormFields, SaveAsStore>({
    title: "输出设置",
    irsFormInfo: stepFormInfo,
    nextTrigger(options) {
      return (
        <OutputFormNextTrigger
          {...options}
          projectId={projectId}
          taskType={TaskType.save_as}
        />
      );
    },
  });

  return stepGen;
}

function SaveAsSteps({ imgInfo }: { imgInfo: ImgInfo }) {
  const { name: imgName, prjId: projectId } = imgInfo;

  const settingStepGen = getSettingStepGen({
    imgInfo: imgInfo,
  });

  const outputStepGen = getOutputStepGen({
    projectId: projectId,
    imgName: imgName,
  });

  const resultStepGen = getTaskResultStepGen({
    projectId: projectId,
  });

  const stepGens = [settingStepGen, outputStepGen, resultStepGen];

  return <IrsSteps stepGens={stepGens} />;
}

function SaveAsModalTrigger({
  children,
  imgInfo,
}: {
  children: ReactNode;
  imgInfo: ImgInfo;
}) {
  return (
    <ModalTrigger
      modalProps={{
        title: "Save as",
        children: <SaveAsSteps imgInfo={imgInfo} />,
      }}
      trigger={children}
    />
  );
}

export function SaveAsMenu({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <>
      <SaveAsModalTrigger imgInfo={imgInfo}>
        <Button type="text" size="small">
          Save as
        </Button>
      </SaveAsModalTrigger>
    </>
  );
}

export function SaveAsIcon({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <Flex vertical align="center" justify="center">
      <SaveAsModalTrigger imgInfo={imgInfo}>
        <Button
          color="default"
          variant="text"
          style={{ width: 54, height: 54 }}
          className="group content-center relative p-0"
        >
          <Image src={IconSaveAs} alt="save-as" className="m-auto" />
        </Button>
      </SaveAsModalTrigger>

      <span className="text-wrap text-xs text-center" style={{ width: 54 }}>
        Save as
      </span>
    </Flex>
  );
}
