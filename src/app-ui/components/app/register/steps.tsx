"use client";

import { addTask } from "@/app-ui/actions/safe-actions/task";
import { StepsComponentProps } from "@/app-ui/components/app/detail";
import StepChooseInput from "@/app-ui/components/app/register/steps/step-choose-input";
import StepChooseOutput from "@/app-ui/components/app/register/steps/step-choose-output";
import {
  NonParamsChannelsNamePath,
  NonParamsFixedImgKeyNamePath,
  StepSetupParameters,
} from "@/app-ui/components/app/register/steps/step-setup-parameters";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";

import { StepProps, Steps, Typography } from "antd";
import { useForm, useWatch } from "antd/es/form/Form";
import { Store } from "antd/es/form/interface";
import { useImperativeHandle, useMemo, useRef } from "react";

const StepNameByIdx = ["input", "parameters", "output"] as const;
type StepName = (typeof StepNameByIdx)[number];

export function RegisterSteps({
  projectId,
  stepIdx,
  initFormData,
  className,
  ref,
}: StepsComponentProps) {
  const [chooseInputForm] = useForm();
  const [setupParametersForm] = useForm();
  const [chooseOutputForm] = useForm();
  const stepValuesRef = useRef<{ [key in StepName]?: unknown }>({});

  const inputImgs = useWatch("inputImgs", chooseInputForm);
  const channels = useWatch(NonParamsChannelsNamePath, setupParametersForm);

  const fixedImgKey = useWatch(
    NonParamsFixedImgKeyNamePath,
    setupParametersForm
  );

  const stepItems = useMemo(() => {
    const {
      input: initInput,
      channels: initChannels,
      output: initOutput,
    } = (initFormData || {}) as {
      input?: Store;
      channels?: Store;
      output?: Store;
    };

    const steps: StepProps[] = [
      {
        title: "选择配准图像",
        description: stepIdx >= 0 && (
          <StepChooseInput
            form={chooseInputForm}
            disabled={stepIdx !== 0}
            initialValues={initInput}
          />
        ),
      },
      {
        title: "设置配准参数",
        description: stepIdx >= 1 && (
          <StepSetupParameters
            form={setupParametersForm}
            disabled={stepIdx !== 1}
            inputImgs={inputImgs ?? []}
            initialValues={initChannels}
          />
        ),
      },
      {
        title: "设置变换策略",
        description: stepIdx >= 2 && (
          <StepChooseOutput
            projectId={projectId}
            form={chooseOutputForm}
            disabled={stepIdx !== 2}
            inputImgs={inputImgs ?? []}
            fixedImgKey={fixedImgKey}
            channels={channels ?? {}}
            initialValues={initOutput}
          />
        ),
      },
      {
        title: "执行配准",
        description: stepIdx >= 3 && (
          <>
            <Typography.Text type="secondary" className="text-wrap">
              根据图片大小和参数设置不同，所需时间会有差异
            </Typography.Text>

            <Typography.Text className="block mt-2">
              配准已启动...
            </Typography.Text>
          </>
        ),
      },
    ];

    return steps;
  }, [
    initFormData,
    stepIdx,
    projectId,
    chooseInputForm,
    setupParametersForm,
    inputImgs,
    chooseOutputForm,
    fixedImgKey,
    channels,
  ]);

  const handleApiErrorDefault = useHandleApiErrorDefault();
  useImperativeHandle(
    ref,
    () => {
      const stepFormsByIdx = [
        chooseInputForm,
        setupParametersForm,
        chooseOutputForm,
      ];

      return {
        async saveStep(stepIdx) {
          const stepValues = await stepFormsByIdx[stepIdx].validateFields();
          stepValuesRef.current[StepNameByIdx[stepIdx]] = stepValues;
        },
        async submitAll() {
          const res = await addTask({
            projectId: projectId,
            taskReq: {
              taskType: "register",
              taskArgs: stepValuesRef.current,
            },
          })
            .then(throwIfSafeActionError)
            .catch(handleApiErrorDefault);

          return { taskId: res.data!.taskId };
        },
      };
    },
    [
      chooseInputForm,
      chooseOutputForm,
      handleApiErrorDefault,
      projectId,
      setupParametersForm,
    ]
  );

  return (
    <Steps
      className={className}
      current={stepIdx}
      direction="vertical"
      items={stepItems}
    />
  );
}
