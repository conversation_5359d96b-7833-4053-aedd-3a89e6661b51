"use client";

import { addTask } from "@/app-ui/actions/safe-actions/task";
import StepChooseChannels from "@/app-ui/components/app/channel-tool/steps/step-choose-channels";
import StepChooseInput from "@/app-ui/components/app/channel-tool/steps/step-choose-input";
import StepChooseOutput from "@/app-ui/components/app/channel-tool/steps/step-choose-output";
import { StepsComponentProps } from "@/app-ui/components/app/detail";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { StepProps, Steps, Typography } from "antd";
import { useForm, useWatch } from "antd/es/form/Form";
import { Store } from "antd/es/form/interface";
import { useImperativeHandle, useMemo, useRef } from "react";

const StepNameByIdx = ["input", "channels", "output"] as const;
type StepName = (typeof StepNameByIdx)[number];

export function ChannelToolSteps({
  projectId,
  stepIdx,
  initFormData,
  className,
  ref,
}: StepsComponentProps) {
  const [chooseInputForm] = useForm();
  const [setupChannelsForm] = useForm();
  const [chooseOutputForm] = useForm();
  const stepValuesRef = useRef<{ [key in StepName]?: unknown }>({});

  const inputImgs = useWatch("inputImgs", chooseInputForm);

  const stepItems = useMemo(() => {
    const {
      input: initInput,
      channels: initChannels,
      output: initOutput,
    } = (initFormData || {}) as {
      input?: Store;
      channels?: Store;
      output?: Store;
    };

    const steps: StepProps[] = [
      {
        title: "选择图像",
        description: stepIdx >= 0 && (
          <StepChooseInput
            form={chooseInputForm}
            disabled={stepIdx !== 0}
            initialValues={initInput}
          />
        ),
      },
      {
        title: "选择通道",
        description: stepIdx >= 1 && (
          <StepChooseChannels
            form={setupChannelsForm}
            disabled={stepIdx !== 1}
            inputImgs={inputImgs ?? []}
            initialValues={initChannels}
          />
        ),
      },
      {
        title: "输出设置",
        description: stepIdx >= 2 && (
          <StepChooseOutput
            projectId={projectId}
            form={chooseOutputForm}
            disabled={stepIdx !== 2}
            inputImgs={inputImgs ?? []}
            initialValues={initOutput}
          />
        ),
      },
      {
        title: "执行操作",
        description: stepIdx >= 3 && (
          <>
            <Typography.Text type="secondary" className="text-wrap">
              根据图片大小和参数设置不同，所需时间会有差异
            </Typography.Text>

            <Typography.Text className="block mt-2">
              操作已启动...
            </Typography.Text>
          </>
        ),
      },
    ];

    return steps;
  }, [
    initFormData,
    stepIdx,
    chooseInputForm,
    setupChannelsForm,
    inputImgs,
    projectId,
    chooseOutputForm,
  ]);
  const handleApiErrorDefault = useHandleApiErrorDefault();
  useImperativeHandle(
    ref,
    () => {
      const stepFormsByIdx = [
        chooseInputForm,
        setupChannelsForm,
        chooseOutputForm,
      ];

      return {
        async saveStep(stepIdx) {
          const stepValues = await stepFormsByIdx[stepIdx].validateFields();
          stepValuesRef.current[StepNameByIdx[stepIdx]] = stepValues;
        },
        async submitAll() {
          const res = await addTask({
            projectId: projectId,
            taskReq: {
              taskType: "channel-tools",
              taskArgs: stepValuesRef.current,
            },
          })
            .then(throwIfSafeActionError)
            .catch(handleApiErrorDefault);
          return { taskId: res.data!.taskId };
        },
      };
    },
    [
      chooseInputForm,
      chooseOutputForm,
      handleApiErrorDefault,
      projectId,
      setupChannelsForm,
    ]
  );

  return (
    <Steps
      className={className}
      current={stepIdx}
      direction="vertical"
      items={stepItems}
    />
  );
}
