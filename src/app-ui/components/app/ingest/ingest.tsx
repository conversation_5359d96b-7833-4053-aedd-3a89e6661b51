"use client";

import { addTask } from "@/app-ui/actions/safe-actions/task";
import { DiskTreeSelector } from "@/app-ui/components/disk/folder-selector";
import { FolderSelector } from "@/app-ui/components/item/folder-selector";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { Button, Form } from "antd";
import path from "path";

export function IngestTask({ projectId }: { projectId: string }) {
  const handleApiErrorDefault = useHandleApiErrorDefault();
  async function onSubmit({ disk, item }: { disk: string; item: string }) {
    const outputName = path.basename(disk);
    const folderId = item.at(-1);
    await addTask({
      projectId,
      taskReq: {
        taskType: "ingest",
        taskArgs: {
          projectId,
          sourcePath: disk,
          outputFolderId: folderId,
          outputName: outputName,
        },
      },
    })
      .then(throwIfSafeActionError)
      .catch(handleApiErrorDefault);
    window.location.reload();
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-auto p-5">
        <Form onFinish={onSubmit} labelCol={{ span: 3 }}>
          <Form.Item
            label="云盘文件"
            name="disk"
            rules={[
              {
                required: true,
                validator(rule, path) {
                  if (path && path.endsWith("/")) {
                    return Promise.reject("请选择文件");
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <DiskTreeSelector projectId={projectId} treeId="folder-selector" />
          </Form.Item>
          <Form.Item
            label="数据组路径"
            name="item"
            rules={[{ required: false }]}
          >
            <FolderSelector prjId={projectId} />
          </Form.Item>
          <Form.Item label={null}>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
}
