"use client";

import { TaskDetailRes } from "@/lib/query/svc";
import { formatDate } from "@/lib/utils";
import { Descriptions, Typography } from "antd";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(duration);
dayjs.extend(relativeTime);

function durationDisplay(
  startAt: dayjs.ConfigType,
  finishedAt: dayjs.ConfigType
) {
  if (!startAt || !finishedAt) {
    return "-";
  }

  return dayjs.duration(dayjs(finishedAt).diff(startAt)).humanize();
}

export function TaskDetail({ taskInfo }: { taskInfo: TaskDetailRes }) {
  return (
    <div>
      <div>任务进度</div>

      <Descriptions
        bordered
        items={[
          {
            label: "任务类型",
            children: taskInfo.task_type,
          },
          {
            label: "任务状态",
            children: taskInfo.remote_task_status,
            span: 2,
          },
          {
            label: "提交时间",
            children: <div>{formatDate(taskInfo.start_at)}</div>,
          },
          {
            label: "结束时间",
            children: <div>{formatDate(taskInfo.finished_at)}</div>,
          },
          {
            label: "耗时",
            children: durationDisplay(taskInfo.start_at, taskInfo.finished_at),
          },
        ]}
      />
      <div>
        <div>错误</div>
        <Typography.Paragraph
          className="whitespace-pre"
          copyable
          ellipsis={{ expandable: true, rows: 3 }}
        >
          {taskInfo.error}
        </Typography.Paragraph>
      </div>
    </div>
  );
}
