"use client";

import { rerunTask } from "@/app-ui/actions/safe-actions/task";
import { paths } from "@/app/route-path";
import { TaskStatus } from "@/lib/domain/task/svc";
import { formatDate } from "@/lib/utils";
import { JsonValue } from "@prisma/client/runtime/library";
import { Button, Table, TableProps } from "antd";
import Link from "next/link";

type Task = {
  task_status: TaskStatus | null;
  error: string | null;
  id: string;
  task_type: string;
  start_at: Date;
  finished_at: Date | null;
  created_by_id: string;
  project_id: string;
  task_args: JsonValue;
  remote_task_id: string | null;
  extra: JsonValue | null;
};

export function TaskList({
  projectId,
  tasks,
}: {
  projectId: string;
  tasks: Task[];
}) {
  async function onRerunTaskClick(taskId: string) {
    await rerunTask({ projectId, taskId });
  }

  const columns: TableProps<Task>["columns"] = [
    {
      title: "id",
      dataIndex: "id",
    },
    {
      title: "type",
      dataIndex: "task_type",
    },
    {
      title: "task_status",
      dataIndex: "task_status",
    },
    {
      title: "start_at",
      dataIndex: "start_at",
      render(v) {
        return formatDate(v);
      },
    },
    {
      title: "finished_at",
      dataIndex: "finished_at",
      render(v) {
        return formatDate(v);
      },
    },
    {
      title: "操作",
      render(d) {
        return (
          <>
            <Link
              href={paths.project.projectId(projectId).task.taskId(d.id).path}
            >
              详情
            </Link>
            <Button
              onClick={() => {
                onRerunTaskClick(d.id);
              }}
            >
              重试
            </Button>
          </>
        );
      },
    },
  ];

  return <Table rowKey={"id"} columns={columns} dataSource={tasks} />;
}
