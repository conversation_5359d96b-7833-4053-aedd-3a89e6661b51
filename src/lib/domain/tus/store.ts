import { StorePaths } from "@/lib/domain/disk/svc";
import { S3Store } from "@tus/s3-store";
import path from "path";

export class IrsS3Store extends S3Store {
  protected infoKey(id: string): string {
    return generateInfoKey(id);
  }
}

function transformDiskPathToInfoPath(diskPath: string) {
  const [projectPath, projectId, , ...res] = diskPath.split("/");
  return path.join(projectPath, StorePaths.info, projectId, ...res);
}

export function generateInfoKey(diskPath: string) {
  const infoPath = transformDiskPathToInfoPath(diskPath);
  return `${infoPath}.info`;
}

if (import.meta.vitest) {
  const { describe, it, expect } = import.meta.vitest;

  describe("transformDiskPathToInfoPath", () => {
    it("transform disk path to info path", () => {
      const diskPath =
        "project/prjId/disk/AFolder/project/3s1a/disk/user/my-file.txt";
      const infoPath = transformDiskPathToInfoPath(diskPath);
      expect(infoPath).toBe(
        "project/disk-upload-tus-info/prjId/AFolder/project/3s1a/disk/user/my-file.txt"
      );
    });
  });

  describe("generateInfoKey", () => {
    it("generate info key", () => {
      const diskPath =
        "project/prjId/disk/AFolder/project/3s1a/disk/user/my-file.txt";
      const infoKey = generateInfoKey(diskPath);
      expect(infoKey).toBe(
        "project/disk-upload-tus-info/prjId/AFolder/project/3s1a/disk/user/my-file.txt.info"
      );
    });
  });
}
