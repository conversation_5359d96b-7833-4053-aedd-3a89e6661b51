import type { AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { DiskPath } from "@/lib/domain/disk/svc";
import { IrsS3Store, generateInfoKey } from "@/lib/domain/tus/store";
import { S3Client } from "@/lib/infra/s3/client";
import { Server as TusServer, type Upload } from "@tus/server";
import path from "path";
import { z } from "zod";

const IRSMetaSchema = z.object({
  relativePath: z.string(),
  projectId: z.string(),
});

type IRSMeta = z.infer<typeof IRSMetaSchema>;

export class TusSvc {
  public tusServer: TusServer;

  constructor(
    private readonly appConfig: AppConfig,
    private readonly s3Client: S3Client
  ) {
    this.tusServer = new TusServer({
      path: "/api/upload",
      namingFunction,
      generateUrl,
      getFileIdFromRequest,
      onUploadFinish: this.onUploadFinish.bind(this),
      datastore: new IrsS3Store({
        s3ClientConfig: {
          region: appConfig.OSS_REGION,
          bucket: appConfig.OSS_BUCKET,
          endpoint: appConfig.OSS_ENDPOINT,
          credentials: {
            accessKeyId: appConfig.OSS_ACCESS_KEY_ID,
            secretAccessKey: appConfig.OSS_SECRET_ACCESS_KEY,
          },
          requestChecksumCalculation: "WHEN_REQUIRED",
          responseChecksumValidation: "WHEN_REQUIRED",
        },
      }),
    });
  }

  async onUploadFinish(req: Request, upload: Upload) {
    const { storage } = upload;
    const returnValue = {};

    if (!storage) {
      return returnValue;
    }
    const infoKey = generateInfoKey(storage.path);
    await this.s3Client.deleteObject(infoKey);
    return returnValue;
  }
}

function generatePath({ projectId, relativePath }: IRSMeta) {
  const ossPath = new DiskPath({ projectId }, relativePath);
  return ossPath.toOSSKey();
}

function extractMetaDataFromReq(req: Request) {
  const metaStr = req.headers.get("irs-meta") ?? "";
  try {
    const metaData = Buffer.from(metaStr, "base64").toString("utf-8");
    const meta = IRSMetaSchema.parse(JSON.parse(metaData));
    return meta;
  } catch {
    throw new DomainError("arg_parse_failed", { meta: metaStr });
  }
}

function namingFunction(req: Request) {
  const meta = extractMetaDataFromReq(req);
  const ossPath = generatePath(meta);
  return ossPath;
}

function generateUrl(req: Request, { path: uploadPath }: { path: string }) {
  const { projectId, relativePath } = extractMetaDataFromReq(req);

  return `${uploadPath}/${encodeURIComponent(
    path.join(projectId, relativePath)
  )}`;
}

function getFileIdFromRequest(req: Request) {
  const meta = extractMetaDataFromReq(req);
  const ossPath = generatePath(meta);
  return ossPath;
}

if (import.meta.vitest) {
  const { describe, it, expect, vi } = await import("vitest");
  describe("TusSvc.onUploadFinish", () => {
    const mockAppConfig = {} as AppConfig;

    it("should delete info file after upload finish", async () => {
      const mockS3Client = {
        deleteObject: vi.fn(),
      } as unknown as S3Client;
      const tusSvc = new TusSvc(mockAppConfig, mockS3Client);
      const upload = {
        storage: {
          path: "project/prjId/disk/666/777/download.zip",
        },
      } as Upload;
      await tusSvc.onUploadFinish({} as Request, upload);
      expect(mockS3Client.deleteObject).toHaveBeenCalledWith(
        "project/disk-upload-tus-info/prjId/666/777/download.zip.info"
      );
    });

    it("should not delete info file if storage is not present", async () => {
      const mockS3Client = {
        deleteObject: vi.fn(),
      } as unknown as S3Client;
      const tusSvc = new TusSvc(mockAppConfig, mockS3Client);
      const upload = {} as Upload;
      await tusSvc.onUploadFinish({} as Request, upload);
      expect(mockS3Client.deleteObject).not.toHaveBeenCalled();
    });
  });
}
