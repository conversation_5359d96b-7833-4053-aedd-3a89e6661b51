import { S3Client } from "@/lib/infra/s3/client";
import path from "path";
import { DomainError } from "@/lib/domain/common/error";
import { genUniqueName, isS3FileAlreadyExistsError } from "@/lib/utils";
import { type LifecycleRule } from "@aws-sdk/client-s3";

export type FileInfo = {
  isDir: false;
  size?: number;
  lastModified?: Date;
  diskPath: string;
};

export type DirInfo = {
  isDir: true;
  diskPath: string;
};

export const StorePaths = {
  project: "project",
  disk: "disk",
  info: "disk-upload-tus-info",
  taskSnapshot: "task-snapshot",
  taskTmpOutputs: "task-tmp-outputs",
};

export type ObjectInfo = FileInfo | DirInfo;

export class DiskSvc {
  private readonly lifecycleRules: LifecycleRule[];

  constructor(private readonly s3Client: S3Client) {
    this.lifecycleRules = [
      {
        ID: "TusInfoCleanup",
        Status: "Enabled",
        Filter: {
          Prefix: path.join(StorePaths.project, StorePaths.info, "/"),
        },
        Expiration: {
          Days: 3,
        },
      },
      {
        ID: "UploadMultipartCleanup",
        Status: "Enabled",
        Filter: {
          Prefix: path.join(StorePaths.project, "/"),
        },
        AbortIncompleteMultipartUpload: {
          DaysAfterInitiation: 3,
        },
      },
      {
        ID: "TaskTmpOutputsCleanup",
        Status: "Enabled",
        Filter: {
          Prefix: path.join(StorePaths.project, StorePaths.taskTmpOutputs, "/"),
        },
        Expiration: {
          Days: 3,
        },
      },
    ];
  }

  async *listFirstLevel({
    projectId,
    prefix,
    isDir,
  }: {
    projectId: string;
    prefix: string;
    isDir?: boolean;
  }) {
    const diskPath = new DiskPath({ projectId }, prefix);

    const objects = this.s3Client.listInfiniteObject({
      prefix: diskPath.toOSSKey(),
      delimiter: "/",
    });

    for await (const { Contents, CommonPrefixes } of objects) {
      for (const { Prefix } of CommonPrefixes) {
        if (!Prefix) {
          continue;
        }

        const objInfo: ObjectInfo = {
          isDir: true,
          diskPath: diskPath.stripToRoot(Prefix),
        };
        yield objInfo;
      }

      if (isDir) {
        return;
      }

      for (const { Key, LastModified, Size } of Contents) {
        if (!Key || Key === diskPath.toOSSKey()) {
          // skip self as this function only list first level
          continue;
        }
        const objInfo: ObjectInfo = {
          isDir: false,
          size: Size,
          lastModified: LastModified,
          diskPath: diskPath.stripToRoot(Key),
        };
        yield objInfo;
      }
    }
  }

  async createDir({
    projectId,
    parentPath,
    name,
  }: {
    projectId: string;
    parentPath: string;
    name: string;
  }) {
    const ossPath = new DiskPath({ projectId }, parentPath, name).toOSSKey();
    const result = await this.s3Client.createDir(ossPath);
    return result;
  }

  async remove({
    projectId,
    pathPart,
    genBaseRootPath,
  }: {
    projectId: string;
    pathPart: string;
    genBaseRootPath?: () => string;
  }) {
    const diskPath = new DiskPath({ projectId, genBaseRootPath }, pathPart);
    const isDirPath = isDir(diskPath.toOSSKey());

    if (isDirPath) {
      const objects = this.s3Client.listInfiniteObject({
        prefix: diskPath.toOSSKey(),
      });

      for await (const { Contents, CommonPrefixes } of objects) {
        const keys = Contents.map(({ Key }) => Key).filter(
          (key) => key !== undefined
        );

        if (keys.length > 0) {
          await this.s3Client.deleteObjects(keys);
        }

        const commonPrefixesKeys = CommonPrefixes.map(
          ({ Prefix }) => Prefix
        ).filter((key) => key !== undefined);

        if (commonPrefixesKeys.length > 0) {
          await this.s3Client.deleteObjects(commonPrefixesKeys);
        }
      }
    } else {
      await this.s3Client.deleteObject(diskPath.toOSSKey());
    }
  }

  private async batchCopyObjects({
    projectId,
    sourcePath,
    keyTransformer,
    forbidOverwrite = false,
  }: {
    projectId: string;
    sourcePath: string;
    keyTransformer: (sourceKey: string) => string;
    forbidOverwrite?: boolean;
  }) {
    const diskSrcPath = new DiskPath({ projectId }, sourcePath).toOSSKey();
    const isDirPath = isDir(diskSrcPath);
    if (isDirPath) {
      const dirs = this.s3Client.listInfiniteObject({
        prefix: diskSrcPath,
      });

      for await (const { Contents } of dirs) {
        for (const content of Contents ?? []) {
          const { Key } = content;
          if (!Key) continue;

          const destinationKey = path.normalize(keyTransformer(Key));
          await this.s3Client.copyObject(Key, destinationKey, forbidOverwrite);
        }
      }
    } else {
      await this.s3Client.copyObject(
        diskSrcPath,
        path.normalize(keyTransformer(diskSrcPath)),
        forbidOverwrite
      );
    }
  }

  async copyTo({
    projectId,
    sourcePath,
    destinationPath,
    forbidOverwrite = false,
    genSourceBaseRootPath,
  }: {
    projectId: string;
    sourcePath: string;
    destinationPath: string;
    forbidOverwrite?: boolean;
    genSourceBaseRootPath?: () => string;
  }) {
    const diskSrcPath = new DiskPath(
      { projectId, genBaseRootPath: genSourceBaseRootPath },
      sourcePath
    ).toOSSKey();
    const diskDstPath = new DiskPath({ projectId }, destinationPath).toOSSKey();
    const diskSrcParentPath = path.dirname(diskSrcPath);

    await this.batchCopyObjects({
      projectId,
      sourcePath,
      keyTransformer: (key) => key.replace(diskSrcParentPath, diskDstPath),
      forbidOverwrite,
    });
  }

  async moveTo({
    projectId,
    sourcePath,
    destinationPath,
    genSourceBaseRootPath,
  }: {
    projectId: string;
    sourcePath: string;
    destinationPath: string;
    genSourceBaseRootPath?: () => string;
  }) {
    const diskSrcPath = new DiskPath(
      { projectId, genBaseRootPath: genSourceBaseRootPath },
      sourcePath
    ).toOSSKey();
    const diskDstPath = new DiskPath({ projectId }, destinationPath).toOSSKey();

    if (getParentFolderDiskPath(diskSrcPath) === diskDstPath) {
      // same level
      return;
    }

    if (diskDstPath.startsWith(diskSrcPath)) {
      throw new DomainError("forbid_to_move_to_sub_dir");
    }

    await this.copyTo({
      projectId,
      sourcePath,
      destinationPath,
      forbidOverwrite: true,
      genSourceBaseRootPath,
    });
    await this.remove({
      projectId,
      pathPart: sourcePath,
      genBaseRootPath: genSourceBaseRootPath,
    });
  }

  async createCopy({
    projectId,
    sourcePath,
  }: {
    projectId: string;
    sourcePath: string;
  }) {
    const diskSrcPath = new DiskPath({ projectId }, sourcePath).toOSSKey();
    const diskSrcParentpath = path.dirname(diskSrcPath);
    const isDirPath = isDir(diskSrcPath);
    const srcName = path.basename(diskSrcPath);
    const uniqueName = genUniqueName(srcName, isDirPath);

    await this.batchCopyObjects({
      projectId,
      sourcePath,
      keyTransformer: (key) =>
        key.replace(
          diskSrcPath,
          path.join(diskSrcParentpath, "/", uniqueName, isDirPath ? "/" : "")
        ),
      forbidOverwrite: true,
    });
  }

  async rename({
    projectId,
    parentPath,
    originName,
    name,
  }: {
    projectId: string;
    parentPath: string;
    originName: string;
    name: string;
  }) {
    const parentDiskPath = new DiskPath({ projectId }, parentPath).toOSSKey();
    const originDiskPath = new DiskPath({ projectId }, parentPath, originName);
    const isDirPath = isDir(originDiskPath.toOSSKey());

    try {
      await this.batchCopyObjects({
        projectId,
        sourcePath: path.join(parentPath, originName),
        keyTransformer: (key) =>
          key.replace(
            originDiskPath.toOSSKey(),
            path.join(parentDiskPath, name, isDirPath ? "/" : "")
          ),
        forbidOverwrite: true,
      });
    } catch (error) {
      if (isS3FileAlreadyExistsError(error)) {
        throw new DomainError("disk_object_already_exists");
      }
      throw error;
    }

    await this.remove({
      projectId,
      pathPart: originDiskPath.toTreeNodePath(),
    });
  }

  async setupCleanupLifecycle() {
    await this.s3Client.putLifecycle(this.lifecycleRules);
  }
}

export class DiskPath {
  private readonly paths: string[];
  private readonly projectId: string;
  private readonly genBaseRootPath?: () => string;

  constructor(
    {
      projectId,
      genBaseRootPath,
    }: {
      projectId: string;
      genBaseRootPath?: () => string;
    },
    ...paths: string[]
  ) {
    this.projectId = projectId;
    this.paths = paths;
    this.genBaseRootPath = genBaseRootPath;
  }

  toOSSKey() {
    return path.join(this.rootPath, ...this.paths);
  }

  toTreeNodePath() {
    return this.stripToRoot(this.toOSSKey());
  }

  get rootPath() {
    let baseRootPath = `${StorePaths.project}/${this.projectId}/${StorePaths.disk}/`;
    if (this.genBaseRootPath) {
      baseRootPath = this.genBaseRootPath();
    }

    return baseRootPath;
  }

  stripToRoot(ossKey: string) {
    return ossKey.replace(this.rootPath, "/");
  }
}

export function isDir(path: string) {
  return path.endsWith("/");
}

export function getParentFolderDiskPath(diskPath: string) {
  const parentDiskPath = path.join(path.dirname(diskPath), "/");
  return parentDiskPath;
}

if (import.meta.vitest) {
  const { it, expect, describe } = import.meta.vitest;

  describe("isDir", () => {
    it("normal file name", () => {
      expect(isDir("a.txt")).toBe(false);
    });

    it("normal folder name", () => {
      expect(isDir("dirA/")).toBe(true);
    });
  });

  describe("DiskPath class", () => {
    it("should generate base root path", () => {
      const diskPath = new DiskPath({ projectId: "project-1" }, "dirA");
      expect(diskPath.rootPath).toBe("project/project-1/disk/");
      const diskPathWithGen = new DiskPath(
        {
          projectId: "project-1",
          genBaseRootPath: () => `project/project-1/disk-gen/`,
        },
        "dirA"
      );
      expect(diskPathWithGen.rootPath).toBe("project/project-1/disk-gen/");
    });

    it("should get oss key correctly", () => {
      const diskPath = new DiskPath({ projectId: "project-1" }, "dirA");
      expect(diskPath.toOSSKey()).toBe("project/project-1/disk/dirA");
      const diskPathWithGen = new DiskPath(
        {
          projectId: "project-1",
          genBaseRootPath: () => `project/project-1/disk-gen/`,
        },
        "dirA"
      );
      expect(diskPathWithGen.toOSSKey()).toBe(
        "project/project-1/disk-gen/dirA"
      );
    });

    it("should strip to root correctly", () => {
      const diskPath = new DiskPath({ projectId: "project-1" }, "dirA");
      expect(diskPath.toTreeNodePath()).toBe("/dirA");
    });
  });
}
