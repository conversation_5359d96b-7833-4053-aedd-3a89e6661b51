import { AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { DiskPath, DiskSvc, StorePaths } from "@/lib/domain/disk/svc";
import { getItem } from "@/lib/domain/item/dbt";
import {
  getTaskSnapshotOssKey,
  ItemQuerySvc,
} from "@/lib/domain/item/query/svc";
import {
  createItem,
  get_item_iid_path,
  get_root_iid_path,
  ItemSvc,
  ItemTypeFlag,
} from "@/lib/domain/item/svc";
import {
  addTask,
  bindRemoteTask,
  getTaskByRemoteTaskId,
  recordTaskError,
  resetTaskStateToStart,
  updateTaskRemoteStatus,
} from "@/lib/domain/task/dbt";
import { Operator } from "@/lib/domain/user/m";
import { RestateClient } from "@/lib/infra/restate/client";
import { S3Client } from "@/lib/infra/s3/client";
import { PrismaClient, Task } from "@prisma/client";
import dayjs from "dayjs";
import { nanoid } from "nanoid";
import path from "path";
import wretch, { type Wretch } from "wretch";
import z from "zod";

export const AddTaskInputSchema = z.object({
  projectId: z.string(),
  taskReq: z.any(),
  snapShot: z.instanceof(Blob).optional(),
});

export const RerunTaskInputSchema = z.object({
  projectId: z.string(),
  taskId: z.string(),
});

export enum TaskType {
  ingest = "ingest",
  rescale_intensity = "rescale-intensity",
  clip_intensity = "clip-intensity",
  bit_depth_convert = "bit-depth-convert",
  flip = "flip",
  save_as = "save-as",
  register = "register",
  merge_channels = "merge-channels",
}

export enum TaskRemoteInnerState {
  NIL = "NIL",
  PENDING_ARGS_AVAIL = "PENDING_ARGS_AVAIL",
  PENDING_NODE_ASSIGNMENT = "PENDING_NODE_ASSIGNMENT",
  PENDING_OBJ_STORE_MEM_AVAIL = "PENDING_OBJ_STORE_MEM_AVAIL",
  PENDING_ARGS_FETCH = "PENDING_ARGS_FETCH",
  SUBMITTED_TO_WORKER = "SUBMITTED_TO_WORKER",
  PENDING_ACTOR_TASK_ARGS_FETCH = "PENDING_ACTOR_TASK_ARGS_FETCH",
  PENDING_ACTOR_TASK_ORDERING_OR_CONCURRENCY = "PENDING_ACTOR_TASK_ORDERING_OR_CONCURRENCY",
  RUNNING = "RUNNING",
  RUNNING_IN_RAY_GET = "RUNNING_IN_RAY_GET",
  RUNNING_IN_RAY_WAIT = "RUNNING_IN_RAY_WAIT",
  FINISHED = "FINISHED",
  FAILED = "FAILED",
}

export enum TaskStatus {
  Running = "Running",
  Finished = "Finished",
  Failed = "Failed",
}

export type TaskReq =
  | IngestTaskReq
  | SaveAsTaskReq
  | RescaleIntensityTaskReq
  | ClipIntensityTaskReq
  | BitDepthConvertTaskReq
  | FlipTaskReq;
export type TaskArgs = TaskReq["taskArgs"];

type IngestTaskReq = {
  taskType: TaskType.ingest;
  taskArgs: {
    projectId: string;
    sourcePath: string;
    outputFolderId?: string;
    outputName: string;
  };
};

type SaveAsTaskReq = {
  taskType: TaskType.save_as;
  taskArgs: {
    projectId: string;
    itemId: string;
    setting: {
      channelChosenType: "shown" | "all";
      splitChannel?: boolean;
      saveAsType: "irs" | "tiff" | "jpg";
      selectedChannels: {
        idx: number;
        name: string;
      }[];
      region?: XYRegion;
    };
    output: {
      folder_name: string;
      tgt_folder_id?: string;
    };
  };
};

type RescaleIntensityTaskReq = {
  taskType: TaskType.rescale_intensity;
  taskArgs: Omit<SaveAsTaskReq["taskArgs"], "setting"> & {
    setting: Omit<SaveAsTaskReq["taskArgs"]["setting"], "selectedChannels"> & {
      selectedChannels: {
        idx: number;
        name: string;
        rescale_range: {
          min: number;
          max: number;
        };
      }[];
    };
  };
};

type ClipIntensityTaskReq = {
  taskType: TaskType.clip_intensity;
  taskArgs: Omit<SaveAsTaskReq["taskArgs"], "setting"> & {
    setting: Omit<SaveAsTaskReq["taskArgs"]["setting"], "selectedChannels"> & {
      selectedChannels: {
        idx: number;
        name: string;
        clip_range: {
          min: number;
          max: number;
        };
      }[];
    };
  };
};

type BitDepthConvertTaskReq = {
  taskType: TaskType.bit_depth_convert;
  taskArgs: SaveAsTaskReq["taskArgs"] & {
    setting: SaveAsTaskReq["taskArgs"]["setting"] & {
      tgt_bit_depth: "uint8" | "uint16" | "float32";
    };
  };
};

type FlipTaskReq = {
  taskType: TaskType.flip;
  taskArgs: SaveAsTaskReq["taskArgs"] & {
    setting: SaveAsTaskReq["taskArgs"]["setting"] & {
      flip_dims: "X" | "Y" | "XY";
    };
  };
};

type IngestExtra = {
  decodedItemId: string;
};

type SaveAsExtra = {
  outputItemIds: string[];
  outputsMoveFolderPath?: string;
};

export type TaskExtra = IngestExtra | SaveAsExtra;

export class TaskSvc {
  private remoteTaskApi: Wretch;
  constructor(
    private readonly itemQuerySvc: ItemQuerySvc,
    private readonly itemSvc: ItemSvc,
    private readonly prisma: PrismaClient,
    appConfig: AppConfig,
    private readonly rsClient: RestateClient,
    private readonly s3Client: S3Client,
    private readonly diskSvc: DiskSvc
  ) {
    this.remoteTaskApi = wretch(appConfig.REMOTE_TASK_API_URL);
  }

  async runIngest({ task, operator }: { task: Task; operator: Operator }) {
    const taskId = task.id;

    await this.itemSvc.clearTaskItems(operator, { taskId });

    const { projectId, sourcePath, outputFolderId, outputName } =
      task.task_args as IngestTaskReq["taskArgs"];

    let output_folder_iid_path = get_root_iid_path(projectId);
    if (outputFolderId) {
      const output_folder = await getItem(this.prisma, {
        itemId: outputFolderId,
        typeFlag: ItemTypeFlag.Folder,
      });

      output_folder_iid_path = get_item_iid_path(output_folder);
    }

    const decodedItem = await createItem(this.prisma, {
      piid_path: output_folder_iid_path,
      typeFlag: ItemTypeFlag.File,
      name: outputName,
      taskId: taskId,
      operator: operator,
    });

    const sourceOssKey = new DiskPath(
      { projectId: projectId },
      sourcePath
    ).toOSSKey();

    const decodedItemTdbGroupUri = await this.itemQuerySvc.getTdbGroupUri(
      decodedItem
    );

    const remoteTaskId = await this.startRemoteTask({
      task_id: taskId,
      remote_task_req: {
        task_type: "ingest",
        task_args: {
          source_path: sourceOssKey,
          output_path: decodedItemTdbGroupUri,
        },
      },
    });

    const extra = {
      decodedItemId: decodedItem.id,
    } as IngestExtra;

    await this.bindRemoteTaskAndUpdateStatus({
      taskId: taskId,
      remoteTaskId: remoteTaskId,
      extra: extra,
    });
  }

  async runFlip({ task, operator }: { task: Task; operator: Operator }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: FlipTaskReq["taskArgs"]) => {
        remoteReq.task_args.req.flip_axes = {
          dims: taskArgs.setting.flip_dims,
        };
      }
    );
  }

  async runRescaleIntensity({
    task,
    operator,
  }: {
    task: Task;
    operator: Operator;
  }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: RescaleIntensityTaskReq["taskArgs"]) => {
        const {
          setting: { selectedChannels: channels },
        } = taskArgs;

        remoteReq.task_args.req.channel_outputs.forEach((co) => {
          co.channel_filters = co.channel_filters.map((ch) => {
            const range = channels.find((c) => c.idx === ch.idx)!.rescale_range;
            return {
              idx: ch.idx,
              rescale_range: [range.min, range.max],
            };
          });
        });
      }
    );
  }

  async runBitDepthConvert({
    task,
    operator,
  }: {
    task: Task;
    operator: Operator;
  }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: BitDepthConvertTaskReq["taskArgs"]) => {
        const {
          setting: { tgt_bit_depth },
        } = taskArgs;
        remoteReq.task_args.req.tgt_bit_depth = tgt_bit_depth;
      }
    );
  }

  async runClipIntensity({
    task,
    operator,
  }: {
    task: Task;
    operator: Operator;
  }) {
    await this._runSaveAsTask(
      task,
      operator,
      (remoteReq, taskArgs: ClipIntensityTaskReq["taskArgs"]) => {
        const {
          setting: { selectedChannels: channels },
        } = taskArgs;

        remoteReq.task_args.req.channel_outputs.forEach((ch) => {
          ch.channel_filters = ch.channel_filters.map((ch) => {
            const range = channels.find((c) => c.idx === ch.idx)!.clip_range;
            return {
              idx: ch.idx,
              clip_range: [range.min, range.max],
            };
          });
        });
      }
    );
  }

  async runSaveAs({ task, operator }: { task: Task; operator: Operator }) {
    await this._runSaveAsTask(task, operator);
  }

  async afterIngest({ task }: { task: Task }) {
    const extra = task.extra as IngestExtra;
    const decodedItemId = extra.decodedItemId;
    await this.itemSvc.updateItemSize({ itemId: decodedItemId });
  }

  async afterSaveAs({ task }: { task: Task }) {
    const { id: taskId, project_id: projectId } = task;
    const { outputItemIds, outputsMoveFolderPath } = task.extra as SaveAsExtra;

    if (outputsMoveFolderPath) {
      await this.diskSvc.moveTo({
        projectId,
        genSourceBaseRootPath: () => getTaskTmpDiskRootPath({ projectId }),
        sourcePath: taskId + "/",
        destinationPath: path.normalize(
          path.join(outputsMoveFolderPath, taskId, "/")
        ),
      });
    }

    for (const outputItemId of outputItemIds) {
      await this.itemSvc.updateItemSize({ itemId: outputItemId });
    }
  }

  async add_task_and_run(
    operator: Operator,
    {
      projectId,
      taskReq,
      snapShot,
    }: {
      projectId: string;
      taskReq: TaskReq;
      snapShot?: Blob;
    }
  ) {
    const uniqueTaskId = nanoid(25);

    if (snapShot) {
      await this.saveSnapShot({ projectId, taskId: uniqueTaskId, snapShot });
    }

    const task = await addTask(this.prisma, {
      projectId: projectId,
      taskReq: taskReq,
      operator: operator,
      taskId: uniqueTaskId,
    });

    await this.runTask({ task, operator });
    return task;
  }

  async rerunTaskById(operator: Operator, { taskId }: { taskId: string }) {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
      },
    });

    if (task) {
      await this.runTask({ task, operator });
    }
  }

  async runTask({ task, operator }: { task: Task; operator: Operator }) {
    const taskId = task.id;
    try {
      await resetTaskStateToStart(this.prisma, {
        task_id: task.id,
        operator,
      });

      switch (task.task_type as TaskType) {
        case TaskType.ingest:
          await this.runIngest({ task, operator });
          break;
        case TaskType.save_as:
          await this.runSaveAs({ task, operator });
          break;
        case TaskType.rescale_intensity:
          await this.runRescaleIntensity({ task, operator });
          break;
        case TaskType.clip_intensity:
          await this.runClipIntensity({ task, operator });
          break;
        case TaskType.bit_depth_convert:
          await this.runBitDepthConvert({ task, operator });
          break;
        case TaskType.flip:
          await this.runFlip({ task, operator });
          break;
        default:
          throw new DomainError("task_type_not_supported", {
            task_type: task.task_type,
          });
      }
    } catch (error) {
      let errorMsg = "系统异常";
      console.error("run task failed: ", taskId, error);

      if (error instanceof DomainError) {
        errorMsg = error.toString();
      }

      try {
        await recordTaskError(this.prisma, {
          task_id: taskId,
          errorMsg,
        });
      } catch (error) {
        console.error("record task error failed in run_task.", taskId, error);
      }
    }
  }

  async startRemoteTask({
    task_id,
    remote_task_req,
  }: {
    task_id: string;
    remote_task_req: RemoteTaskReq;
  }) {
    try {
      const remoteTaskId = await this.remoteTaskApi
        .url(`/start-task/${task_id}`)
        .post(remote_task_req)
        .json<string>();

      return remoteTaskId;
    } catch (err) {
      console.error(`failed to call start-ray-task`, {
        task_id,
        remote_task_req,
        err,
      });

      throw new DomainError("start_task_api_failed", { cause: String(err) });
    }
  }

  async processAfterTaskDone({ taskId }: { taskId: string }) {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
      },
    });
    if (!task) {
      return;
    }

    const taskType = task.task_type;
    try {
      switch (taskType as TaskType) {
        case TaskType.ingest:
          await this.afterIngest({ task });
          break;
        case TaskType.save_as:
        case TaskType.rescale_intensity:
        case TaskType.clip_intensity:
        case TaskType.bit_depth_convert:
        case TaskType.flip:
          await this.afterSaveAs({ task });
          break;
        default:
          // todo: raise error to restate?
          throw new Error(
            `task_type ${taskType} is not supported for processing after task done for ${taskId}`
          );
      }
    } catch (error) {
      console.error("processAfterTaskDone failed: ", error, task);
      // todo: raise error to restate?
    }
  }

  async bindRemoteTaskAndUpdateStatus({
    taskId,
    remoteTaskId,
    extra,
  }: {
    taskId: string;
    remoteTaskId: string;
    extra?: TaskExtra;
  }) {
    await bindRemoteTask(this.prisma, {
      task_id: taskId,
      remote_task_id: remoteTaskId,
      extra: extra,
    });

    await this.rsClient.taskSvc.sendClient.updateRemoteTaskStatus({
      taskId,
      remoteTaskId,
    });
  }

  async updateRemoteTaskStatus({
    taskId,
    remoteTaskId,
  }: {
    taskId: string;
    remoteTaskId: string;
  }): Promise<{ keepPolling: boolean }> {
    const task = await getTaskByRemoteTaskId(this.prisma, {
      remoteTaskId,
      taskId,
    });

    if (!task) {
      return { keepPolling: false };
    }

    const timeoutSecondForLostState = 30;
    const taskDurationInSecond = dayjs().diff(task.start_at, "second");

    const remoteTaskState = await this.getRemoteTaskState({ remoteTaskId });

    if (remoteTaskState == null) {
      if (taskDurationInSecond > timeoutSecondForLostState) {
        await recordTaskError(this.prisma, {
          task_id: taskId,
          errorMsg: "计算服务重启，请重试任务",
        });

        return { keepPolling: false };
      }

      return { keepPolling: true };
    }

    const remoteTaskStatus = remoteTaskState.state;

    if (remoteTaskStatus == TaskRemoteInnerState.FAILED) {
      const errorMsg = remoteTaskState.error_message;
      await recordTaskError(this.prisma, {
        task_id: taskId,
        errorMsg: errorMsg,
        remote_task_status: remoteTaskStatus,
      });

      return { keepPolling: false };
    } else if (remoteTaskStatus == TaskRemoteInnerState.FINISHED) {
      await this.rsClient.taskSvc.sendClient.processAfterTaskDone({ taskId });

      return { keepPolling: false };
    } else {
      await updateTaskRemoteStatus(this.prisma, {
        taskId,
        remoteTaskStatus,
      });

      return { keepPolling: true };
    }
  }

  async getRemoteTaskState({ remoteTaskId }: { remoteTaskId: string }) {
    const state = await this.remoteTaskApi
      .url(`/task-state/${remoteTaskId}`)
      .get()
      .json<{
        state: TaskRemoteInnerState;
        creation_time_ms?: number;
        start_time_ms?: number;
        end_time_ms?: number;
        error_message?: string;
      } | null>();
    return state;
  }

  async saveSnapShot({
    projectId,
    taskId,
    snapShot,
  }: {
    projectId: string;
    taskId: string;
    snapShot: Blob;
  }) {
    const snapshotOssKey = getTaskSnapshotOssKey({
      projectId,
      taskId,
    });
    await this.s3Client.putObject(
      snapshotOssKey,
      Buffer.from(await snapShot.arrayBuffer()),
      "image/svg+xml"
    );
  }

  async _runSaveAsTask<T extends SaveAsTaskReq["taskArgs"]>(
    task: Task,
    operator: Operator,
    enhanceRemoteReq?: (remoteReq: RemoteTaskSaveAsReq, taskArgs: T) => void
  ) {
    const taskId = task.id;
    const taskArgs = task.task_args as T;

    const { remoteReq, extra } = await this._prepareBaseSaveAs({
      taskId,
      taskArgs,
      operator,
    });

    enhanceRemoteReq?.(remoteReq, taskArgs);

    const remoteTaskId = await this.startRemoteTask({
      task_id: taskId,
      remote_task_req: remoteReq,
    });

    await this.bindRemoteTaskAndUpdateStatus({
      taskId: taskId,
      remoteTaskId: remoteTaskId,
      extra,
    });
  }

  async _prepareBaseSaveAs({
    taskId,
    taskArgs,
    operator,
  }: {
    taskId: string;
    taskArgs: SaveAsTaskReq["taskArgs"];
    operator: Operator;
  }) {
    const {
      projectId,
      itemId,
      setting: { saveAsType, selectedChannels: channels, region, splitChannel },
      output: { folder_name, tgt_folder_id },
    } = taskArgs;

    await this.itemSvc.clearTaskItems(operator, { taskId });
    await this.diskSvc.remove({
      projectId,
      pathPart: taskId + "/",
      genBaseRootPath: () => getTaskTmpDiskRootPath({ projectId }),
    });

    const sourceItem = await getItem(this.prisma, {
      itemId,
      typeFlag: ItemTypeFlag.File,
    });

    const extra: SaveAsExtra = {
      outputItemIds: [],
    };

    const src_img_id = await this.itemQuerySvc.getTdbGroupUri(sourceItem);

    const remoteReq: RemoteTaskSaveAsReq = {
      task_type: "save_as",
      task_args: {
        req: {
          src_img_id: src_img_id,
          tgt_type: saveAsType,
          channel_outputs: [],
          xy_region: region,
        },
      },
    };

    const sorted_channels = channels.sort((a, b) => a.idx - b.idx);

    if (saveAsType === "irs") {
      let output_folder_iid_path = sourceItem.piid_path;
      if (tgt_folder_id) {
        const output_folder = await getItem(this.prisma, {
          itemId: tgt_folder_id,
          typeFlag: ItemTypeFlag.Folder,
        });
        output_folder_iid_path = get_item_iid_path(output_folder);
      }

      const store_folder = await createItem(this.prisma, {
        piid_path: output_folder_iid_path,
        typeFlag: ItemTypeFlag.Folder,
        name: folder_name,
        taskId,
        operator,
      });

      await genChannelOutputs(async (item_name) => {
        const output_item = await createItem(this.prisma, {
          piid_path: get_item_iid_path(store_folder),
          typeFlag: ItemTypeFlag.File,
          operator,
          name: item_name,
          taskId,
        });
        extra.outputItemIds.push(output_item.id);
        const tdb_group_uri = await this.itemQuerySvc.getTdbGroupUri(
          output_item
        );
        return tdb_group_uri;
      });
    } else {
      await genChannelOutputs((item_name) => {
        const sub_path = new DiskPath(
          {
            projectId,
            genBaseRootPath: () => getTaskTmpDiskRootPath({ projectId }),
          },
          taskId,
          item_name
        ).toOSSKey();
        return sub_path;
      });

      extra.outputsMoveFolderPath = tgt_folder_id || "/";
    }

    return { remoteReq, extra };

    async function genChannelOutputs(
      genSubPath: (item_name: string) => string | Promise<string>
    ) {
      if (splitChannel) {
        for (const ch of sorted_channels) {
          const item_name = replaceSuffix(
            `${ch.idx}-${ch.name}-${folder_name}`,
            saveAsType
          );
          const sub_path = await genSubPath(item_name);
          remoteReq.task_args.req.channel_outputs.push({
            sub_path,
            channel_filters: [{ idx: ch.idx }],
          });
        }
      } else {
        const item_name = replaceSuffix(folder_name, saveAsType);
        const sub_path = await genSubPath(item_name);
        remoteReq.task_args.req.channel_outputs.push({
          sub_path,
          channel_filters: sorted_channels.map((ch) => ({
            idx: ch.idx,
          })),
        });
      }
    }
  }
}

function replaceSuffix(name: string, suffix: string) {
  const orgSuffix = path.extname(name);
  const baseName = path.basename(name, orgSuffix);
  const newName = `${baseName}.${suffix}`;
  return newName;
}

function getTaskTmpDiskRootPath({ projectId }: { projectId: string }) {
  const rootPath = path.join(
    StorePaths.project,
    StorePaths.taskTmpOutputs,
    projectId
  );
  return rootPath;
}

export function convertTaskRemoteStateToStatus(state: TaskRemoteInnerState) {
  switch (state) {
    case TaskRemoteInnerState.FINISHED:
      return TaskStatus.Finished;
    case TaskRemoteInnerState.FAILED:
      return TaskStatus.Failed;
    default:
      return TaskStatus.Running;
  }
}

type RemoteTaskReq = RemoteTaskIngestReq | RemoteTaskSaveAsReq;

type RemoteTaskIngestReq = {
  task_type: "ingest";
  task_args: {
    source_path: string;
    output_path: string;
  };
};

type ChannelFilter = {
  idx: number;
  rescale_range?: [number, number];
  clip_range?: [number, number];
};

type ChannelOutput = {
  sub_path: string;
  channel_filters: ChannelFilter[];
};

type XYRegion = {
  x: number;
  y: number;
  width: number;
  height: number;
};

type RemoteTaskSaveAsReq = {
  task_type: "save_as";
  task_args: {
    req: {
      src_img_id: string;
      tgt_type: string;
      channel_outputs: ChannelOutput[];
      xy_region?: XYRegion;
      tgt_bit_depth?: string;
      flip_axes?: { dims: "X" | "Y" | "XY" };
    };
  };
};

if (import.meta.vitest) {
  const { it, expect, describe, vi } = import.meta.vitest;
  const { mock } = await import("vitest-mock-extended");
  const { http, HttpResponse } = await import("msw");
  const { server } = await import("@/mocks/server");

  const REMOTE_TASK_API_URL = "https://localhost:2345";
  const itemQuerySvc = mock<ItemQuerySvc>();
  const itemSvc = mock<ItemSvc>();
  const prisma = mock<PrismaClient>();
  const appConfig = mock<AppConfig>({
    REMOTE_TASK_API_URL: REMOTE_TASK_API_URL,
  });
  const restateClient = mock<RestateClient>();
  const s3Client = mock<S3Client>();
  const diskSvc = mock<DiskSvc>();

  const getSUT = async () => {
    vi.resetModules();
    const { TaskSvc } = await import("./svc");

    const svc = new TaskSvc(
      itemQuerySvc,
      itemSvc,
      prisma,
      appConfig,
      restateClient,
      s3Client,
      diskSvc
    );
    return svc;
  };

  describe("replaceSuffix", () => {
    it("should replace suffix", () => {
      expect(replaceSuffix("test.test.txt", "jpg")).toBe("test.test.jpg");
      expect(replaceSuffix("test", "jpg")).toBe("test.jpg");
      expect(replaceSuffix("test.txt", "jpg")).toBe("test.jpg");
    });

    it("should return object name", () => {
      expect(replaceSuffix("/folder/test", "jpg")).toBe("test.jpg");
    });
  });

  describe("_prepareBaseSaveAs", async () => {
    it("should prepare base save as when saveAsType is irs", async () => {
      const { createItem } = vi.hoisted(() => ({
        createItem: vi.fn(),
      }));

      vi.doMock("@/lib/domain/item/dbt", () => ({
        getItem: vi
          .fn()
          .mockResolvedValueOnce({
            iid: "2",
            piid_path: "/prjd-id/1/",
            name: "xxx.irs",
          })
          .mockResolvedValueOnce({
            iid: "3",
            piid_path: "/prjd-id/1/",
            name: "tgt-folder",
          }),
      }));

      vi.doMock("@/lib/domain/item/svc", async (importOriginal) => ({
        ...(await importOriginal()),
        createItem: createItem
          .mockResolvedValueOnce({
            iid: "4",
            piid_path: "/prjd-id/1/",
            name: "output-folder",
            id: "output-folder-id",
          })
          .mockResolvedValueOnce({
            iid: "5",
            piid_path: "/prjd-id/1/",
            name: "channel-1-xx.irs",
            id: "channel-1-file-id",
          })
          .mockResolvedValueOnce({
            iid: "6",
            piid_path: "/prjd-id/1/",
            name: "channel-2-xx.irs",
            id: "channel-2-file-id",
          }),
      }));

      const taskSvc = await getSUT();

      // @ts-expect-error: Accessing private property for test mocking
      taskSvc.itemQuerySvc.getTdbGroupUri = vi
        .fn()
        .mockResolvedValueOnce("s3://test-bkt/xxx.irs")
        .mockResolvedValueOnce("s3://test-bkt/channel-1-file.irs")
        .mockResolvedValueOnce("s3://test-bkt/channel-2-file.irs");

      const taskId = "uuid-task-id-kasgbaq1223a";
      const taskArgs: SaveAsTaskReq["taskArgs"] = {
        projectId: "prj-id",
        itemId: "item-id",
        setting: {
          channelChosenType: "shown",
          splitChannel: true,
          saveAsType: "irs",
          selectedChannels: [
            { idx: 0, name: "channel-name-1" },
            { idx: 1, name: "channel-name-1" },
          ],
          region: {
            x: 1463,
            y: 394,
            width: 802,
            height: 491,
          },
        },
        output: {
          folder_name: "folder-name",
          tgt_folder_id: "tgt-folder-id",
        },
      };
      const result = await taskSvc._prepareBaseSaveAs({
        taskId,
        taskArgs,
        operator: {
          id: "userId",
          isTestAccount: false,
        },
      });
      expect(result).toEqual({
        extra: {
          outputItemIds: ["channel-1-file-id", "channel-2-file-id"],
        },
        remoteReq: {
          task_type: "save_as",
          task_args: {
            req: {
              src_img_id: "s3://test-bkt/xxx.irs",
              tgt_type: "irs",
              channel_outputs: [
                {
                  sub_path: "s3://test-bkt/channel-1-file.irs",
                  channel_filters: [{ idx: 0 }],
                },
                {
                  sub_path: "s3://test-bkt/channel-2-file.irs",
                  channel_filters: [{ idx: 1 }],
                },
              ],
              xy_region: { x: 1463, y: 394, width: 802, height: 491 },
            },
          },
        },
      });
    });

    it("should prepare base save as when saveAsType is not irs", async () => {
      vi.doMock("@/lib/domain/item/dbt", () => ({
        getItem: vi.fn().mockResolvedValueOnce({
          iid: "2",
          piid_path: "/prjd-id/1/",
          name: "xxx.irs",
        }),
      }));

      const taskSvc = await getSUT();
      // @ts-expect-error: Accessing private property for test mocking
      taskSvc.itemQuerySvc.getTdbGroupUri = vi
        .fn()
        .mockResolvedValueOnce("s3://test-bkt/xxx.irs");
      const taskId = "uuid-task-id-kasgbaq1223a";
      const taskArgs: SaveAsTaskReq["taskArgs"] = {
        projectId: "prj-id",
        itemId: "item-id",
        setting: {
          channelChosenType: "shown",
          splitChannel: true,
          saveAsType: "jpg",
          selectedChannels: [
            { idx: 0, name: "channel-name-1" },
            { idx: 1, name: "channel-name-1" },
          ],
        },
        output: {
          folder_name: "folder-name",
          tgt_folder_id: "/dataSet/数据组1",
        },
      };

      const result = await taskSvc._prepareBaseSaveAs({
        taskId,
        taskArgs,
        operator: {
          id: "userId",
          isTestAccount: false,
        },
      });

      expect(result).toEqual({
        extra: {
          outputItemIds: [],
          outputsMoveFolderPath: "/dataSet/数据组1",
        },
        remoteReq: {
          task_type: "save_as",
          task_args: {
            req: {
              src_img_id: "s3://test-bkt/xxx.irs",
              tgt_type: "jpg",
              channel_outputs: [
                {
                  sub_path: `project/task-tmp-outputs/prj-id/${taskId}/0-channel-name-1-folder-name.jpg`,
                  channel_filters: [{ idx: 0 }],
                },
                {
                  sub_path: `project/task-tmp-outputs/prj-id/${taskId}/1-channel-name-1-folder-name.jpg`,
                  channel_filters: [{ idx: 1 }],
                },
              ],
              xy_region: undefined,
            },
          },
        },
      });
    });
  });

  describe("runFlip", async () => {
    it("should run flip", async () => {
      vi.doMock("@/lib/domain/item/dbt", () => ({
        getItem: vi.fn().mockResolvedValueOnce({
          iid: "2",
          piid_path: "/prjd-id/1/",
          name: "xxx.irs",
        }),
      }));

      const taskSvc = await getSUT();

      // @ts-expect-error: Accessing private property for test mocking
      taskSvc.itemQuerySvc.getTdbGroupUri = vi
        .fn()
        .mockResolvedValueOnce("s3://test-bkt/xxx.irs");

      const startRemoteTask = vi
        .spyOn(taskSvc, "startRemoteTask")
        .mockImplementation(vi.fn().mockResolvedValueOnce("remote-task-id"));
      vi.spyOn(taskSvc, "bindRemoteTaskAndUpdateStatus").mockImplementation(
        vi.fn()
      );

      const taskId = "uuid-task-id-kasgbaq1223a";
      const taskArgs: FlipTaskReq["taskArgs"] = {
        projectId: "prj-id",
        itemId: "item-id",
        setting: {
          channelChosenType: "shown",
          splitChannel: true,
          saveAsType: "jpg",
          selectedChannels: [
            { idx: 0, name: "channel-name-1" },
            { idx: 1, name: "channel-name-1" },
          ],
          flip_dims: "X",
        },
        output: {
          folder_name: "folder-name",
          tgt_folder_id: "/dataSet/数据组1",
        },
      };

      await taskSvc.runFlip({
        task: {
          id: taskId,
          task_type: TaskType.flip,
          task_args: taskArgs,
        } as unknown as Task,
        operator: { id: "userId", isTestAccount: false },
      });

      expect(startRemoteTask).toHaveBeenCalledWith({
        task_id: taskId,
        remote_task_req: {
          task_type: "save_as",
          task_args: {
            req: {
              src_img_id: "s3://test-bkt/xxx.irs",
              tgt_type: "jpg",
              channel_outputs: [
                {
                  sub_path: `project/task-tmp-outputs/prj-id/${taskId}/0-channel-name-1-folder-name.jpg`,
                  channel_filters: [{ idx: 0 }],
                },
                {
                  sub_path: `project/task-tmp-outputs/prj-id/${taskId}/1-channel-name-1-folder-name.jpg`,
                  channel_filters: [{ idx: 1 }],
                },
              ],
              flip_axes: { dims: "X" },
            },
          },
        },
      });
    });
  });

  describe("runRescaleIntensity", () => {
    it("should run rescale intensity", async () => {
      vi.doMock("@/lib/domain/item/dbt", () => ({
        getItem: vi.fn().mockResolvedValueOnce({
          iid: "2",
          piid_path: "/prjd-id/1/",
          name: "xxx.irs",
        }),
      }));

      const taskSvc = await getSUT();

      // @ts-expect-error: Accessing private property for test mocking
      taskSvc.itemQuerySvc.getTdbGroupUri = vi
        .fn()
        .mockResolvedValueOnce("s3://test-bkt/xxx.irs");

      const startRemoteTask = vi
        .spyOn(taskSvc, "startRemoteTask")
        .mockImplementation(vi.fn().mockResolvedValueOnce("remote-task-id"));
      vi.spyOn(taskSvc, "bindRemoteTaskAndUpdateStatus").mockImplementation(
        vi.fn()
      );

      const taskId = "uuid-task-id-kasgbaq1223a";
      const taskArgs: RescaleIntensityTaskReq["taskArgs"] = {
        projectId: "prj-id",
        itemId: "item-id",
        setting: {
          channelChosenType: "shown",
          splitChannel: true,
          saveAsType: "jpg",
          selectedChannels: [
            {
              idx: 0,
              name: "channel-name-1",
              rescale_range: { min: 0, max: 255 },
            },
            {
              idx: 1,
              name: "channel-name-1",
              rescale_range: { min: 0, max: 255 },
            },
          ],
        },
        output: {
          folder_name: "folder-name",
          tgt_folder_id: "/dataSet/数据组1",
        },
      };

      await taskSvc.runRescaleIntensity({
        task: {
          id: taskId,
          task_type: TaskType.rescale_intensity,
          task_args: taskArgs,
        } as unknown as Task,
        operator: { id: "userId", isTestAccount: false },
      });

      expect(startRemoteTask).toHaveBeenCalledWith({
        task_id: taskId,
        remote_task_req: {
          task_type: "save_as",
          task_args: {
            req: {
              src_img_id: "s3://test-bkt/xxx.irs",
              tgt_type: "jpg",
              channel_outputs: [
                {
                  sub_path: `project/task-tmp-outputs/prj-id/${taskId}/0-channel-name-1-folder-name.jpg`,
                  channel_filters: [{ idx: 0, rescale_range: [0, 255] }],
                },
                {
                  sub_path: `project/task-tmp-outputs/prj-id/${taskId}/1-channel-name-1-folder-name.jpg`,
                  channel_filters: [{ idx: 1, rescale_range: [0, 255] }],
                },
              ],
            },
          },
        },
      });
    });
  });

  describe("updateRemoteTaskStatus", () => {
    it("should return false when task is not found", async () => {
      vi.doMock("@/lib/domain/task/dbt", () => ({
        getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce(null),
      }));

      const taskSvc = await getSUT();
      const result = await taskSvc.updateRemoteTaskStatus({
        taskId: "task-id",
        remoteTaskId: "remote-task-id",
      });
      expect(result).toEqual({ keepPolling: false });
    });

    it("should not keep polling when remote task state is null & taskDurationInSecond > timeoutSecondForLostState", async () => {
      vi.doMock("dayjs", () => ({
        default: vi.fn(() => ({
          diff: vi.fn().mockReturnValue(32),
        })),
      }));

      const mockRecordTaskError = vi.fn();

      vi.doMock("@/lib/domain/task/dbt", () => ({
        getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
          id: "task-id",
          start_at: new Date("2025-01-01T01:23:00.000Z"),
        }),
        recordTaskError: mockRecordTaskError,
      }));
      const taskSvc = await getSUT();
      server.use(
        http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
          return HttpResponse.json(null);
        })
      );

      const result = await taskSvc.updateRemoteTaskStatus({
        taskId: "task-id",
        remoteTaskId: "remote-task-id",
      });

      expect(result).toEqual({ keepPolling: false });
      expect(mockRecordTaskError).toHaveBeenCalledWith(prisma, {
        task_id: "task-id",
        errorMsg: "计算服务重启，请重试任务",
      });
    });

    it("should  keep polling when remote task state is null & taskDurationInSecond <= timeoutSecondForLostState", async () => {
      vi.doMock("dayjs", () => ({
        default: vi.fn(() => ({
          diff: vi.fn().mockReturnValue(29),
        })),
      }));

      vi.doMock("@/lib/domain/task/dbt", () => ({
        getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
          id: "task-id",
          start_at: new Date("2025-01-01T01:23:00.000Z"),
        }),
      }));
      const taskSvc = await getSUT();
      server.use(
        http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
          return HttpResponse.json(null);
        })
      );

      const result = await taskSvc.updateRemoteTaskStatus({
        taskId: "task-id",
        remoteTaskId: "remote-task-id",
      });

      expect(result).toEqual({ keepPolling: true });
    });

    it("should record task error when remote task status is failed", async () => {
      const mockRecordTaskError = vi.fn();

      vi.doMock("@/lib/domain/task/dbt", () => ({
        getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
          id: "task-id",
        }),
        recordTaskError: mockRecordTaskError,
      }));

      const taskSvc = await getSUT();

      server.use(
        http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
          return HttpResponse.json({
            state: TaskRemoteInnerState.FAILED,
            error_message: "error_message",
          });
        })
      );

      const result = await taskSvc.updateRemoteTaskStatus({
        taskId: "task-id",
        remoteTaskId: "remote-task-id",
      });
      expect(result).toEqual({ keepPolling: false });
      expect(mockRecordTaskError).toHaveBeenCalledWith(prisma, {
        task_id: "task-id",
        errorMsg: "error_message",
        remote_task_status: TaskRemoteInnerState.FAILED,
      });
    });

    it("should not keep polling when remote task status is finished", async () => {
      vi.doMock("@/lib/domain/task/dbt", () => ({
        getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
          id: "task-id",
        }),
      }));

      const taskSvc = await getSUT();
      const mockProcessAfterTaskDone = vi.fn();
      // @ts-expect-error: Accessing private property for test mocking
      taskSvc.rsClient.taskSvc = {
        sendClient: {
          processAfterTaskDone: mockProcessAfterTaskDone,
        },
      };
      server.use(
        http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
          return HttpResponse.json({
            state: TaskRemoteInnerState.FINISHED,
          });
        })
      );

      const result = await taskSvc.updateRemoteTaskStatus({
        taskId: "task-id",
        remoteTaskId: "remote-task-id",
      });
      expect(result).toEqual({ keepPolling: false });
      expect(mockProcessAfterTaskDone).toHaveBeenCalledWith({
        taskId: "task-id",
      });
    });

    it("should keep polling & update remote task status when remote task status is running", async () => {
      const mockUpdateTaskRemoteStatus = vi.fn();
      vi.doMock("@/lib/domain/task/dbt", () => ({
        getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
          id: "task-id",
        }),
        updateTaskRemoteStatus: mockUpdateTaskRemoteStatus,
      }));
      const taskSvc = await getSUT();

      server.use(
        http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
          return HttpResponse.json({
            state: TaskRemoteInnerState.PENDING_NODE_ASSIGNMENT,
          });
        })
      );

      const result = await taskSvc.updateRemoteTaskStatus({
        taskId: "task-id",
        remoteTaskId: "remote-task-id",
      });
      expect(result).toEqual({ keepPolling: true });
      expect(mockUpdateTaskRemoteStatus).toHaveBeenCalledWith(prisma, {
        taskId: "task-id",
        remoteTaskStatus: TaskRemoteInnerState.PENDING_NODE_ASSIGNMENT,
      });
    });
  });

  describe("afterSaveAs", () => {
    it("should update item size", async () => {
      const taskSvc = await getSUT();
      const mockUpdateItemSize = vi.fn();
      // @ts-expect-error: Accessing private property for test mocking
      taskSvc.itemSvc.updateItemSize = mockUpdateItemSize;
      await taskSvc.afterSaveAs({
        task: {
          id: "task-id",
          project_id: "project-id",
          extra: {
            outputItemIds: ["item-id", "item-id-2"],
          },
        } as unknown as Task,
      });
      expect(mockUpdateItemSize).toHaveBeenCalledTimes(2);
      expect(mockUpdateItemSize).toHaveBeenCalledWith({
        itemId: "item-id",
      });
      expect(mockUpdateItemSize).toHaveBeenCalledWith({
        itemId: "item-id-2",
      });
    });

    it("should move temp outputs to target folder", async () => {
      const taskSvc = await getSUT();
      const mockMoveTo = vi.fn();
      // @ts-expect-error: Accessing private property for test mocking
      taskSvc.diskSvc.moveTo = mockMoveTo;
      await taskSvc.afterSaveAs({
        task: {
          id: "task-id",
          project_id: "project-id-5",
          extra: {
            outputItemIds: [],
            outputsMoveFolderPath: "/set/folder/",
          },
        } as unknown as Task,
      });

      expect(mockMoveTo).toHaveBeenCalledWith({
        projectId: "project-id-5",
        genSourceBaseRootPath: expect.any(Function),
        sourcePath: "task-id/",
        destinationPath: "/set/folder/task-id/",
      });

      const callArgs = mockMoveTo.mock.calls[0][0];
      const genSourceBaseRootPathFn = callArgs.genSourceBaseRootPath;
      expect(genSourceBaseRootPathFn()).toBe(
        getTaskTmpDiskRootPath({ projectId: "project-id-5" })
      );
    });
  });

  describe("getTaskTmpDiskRootPath", () => {
    it("should return the correct task running disk root path", () => {
      const rootPath = getTaskTmpDiskRootPath({ projectId: "project-id" });
      expect(rootPath).toBe("project/task-tmp-outputs/project-id");
    });
  });
}
