import { getTasksByPrjId } from "@/lib/domain/task/dbt";
import { Prisma, PrismaClient } from "@prisma/client";
import {
  convertTaskRemoteStateToStatus,
  TaskRemoteInnerState,
  TaskStatus,
} from "@/lib/domain/task/svc";

export class TaskQuerySvc {
  constructor(private readonly prisma: PrismaClient) {}

  async tasks({
    prjId,
    status,
    sortBy = "start_at",
    order = "desc",
    length,
  }: {
    prjId: string;
    status?: TaskStatus;
    sortBy?: "start_at" | "finished_at";
    order?: "asc" | "desc";
    length?: number;
  }) {
    let remoteStatus: Prisma.TaskWhereInput["remote_task_status"];
    let finishedAt: Prisma.TaskWhereInput["finished_at"];

    switch (status) {
      case TaskStatus.Failed:
        remoteStatus = TaskRemoteInnerState.FAILED;
        break;

      case TaskStatus.Finished:
        remoteStatus = TaskRemoteInnerState.FINISHED;
        break;

      case TaskStatus.Running:
        remoteStatus = { not: null };
        finishedAt = null;
        break;

      default:
        break;
    }

    const tasks = await getTasksByPrjId(this.prisma, {
      prjId,
      orderBy: { [sortBy]: order },
      length,
      remoteStatus,
      finishedAt,
    });

    const convertedTasks = tasks.map((task) => ({
      ...task,
      task_status: task.remote_task_status
        ? convertTaskRemoteStateToStatus(
            task.remote_task_status as TaskRemoteInnerState
          )
        : null,
    }));
    return convertedTasks;
  }
}

if (import.meta.vitest) {
  const { describe, expect, it, vi } = await import("vitest");

  describe("tasks", () => {
    const prisma = {} as PrismaClient;
    const { getTasksByPrjId } = vi.hoisted(() => ({
      getTasksByPrjId: vi.fn(),
    }));
    getTasksByPrjId.mockResolvedValue([
      {
        id: "uuidxa",
        remote_task_status: TaskRemoteInnerState.PENDING_ARGS_AVAIL,
      },
    ]);

    vi.mock("@/lib/domain/task/dbt", () => ({
      getTasksByPrjId: getTasksByPrjId,
    }));
    const taskQuerySvc = new TaskQuerySvc(prisma);

    it("should convert task remote state to status", async () => {
      await taskQuerySvc.tasks({
        prjId: "prjId",
        sortBy: "start_at",
        order: "desc",
        length: 6,
        status: TaskStatus.Failed,
      });

      expect(getTasksByPrjId).toHaveBeenCalledWith(prisma, {
        prjId: "prjId",
        orderBy: { start_at: "desc" },
        length: 6,
        remoteStatus: TaskRemoteInnerState.FAILED,
        finishedAt: undefined,
      });

      await taskQuerySvc.tasks({
        prjId: "prjId",
        sortBy: "start_at",
        order: "desc",
        length: 6,
        status: TaskStatus.Finished,
      });

      expect(getTasksByPrjId).toHaveBeenCalledWith(prisma, {
        prjId: "prjId",
        orderBy: { start_at: "desc" },
        length: 6,
        remoteStatus: TaskRemoteInnerState.FINISHED,
        finishedAt: undefined,
      });

      await taskQuerySvc.tasks({
        prjId: "prjId",
        sortBy: "start_at",
        order: "desc",
        length: 6,
        status: TaskStatus.Running,
      });

      expect(getTasksByPrjId).toHaveBeenCalledWith(prisma, {
        prjId: "prjId",
        orderBy: { start_at: "desc" },
        length: 6,
        remoteStatus: { not: null },
        finishedAt: null,
      });
    });

    it("should convert task remote state", async () => {
      const tasks = await taskQuerySvc.tasks({
        prjId: "prjId",
        sortBy: "start_at",
        order: "desc",
        length: 6,
        status: TaskStatus.Running,
      });

      expect(tasks[0].task_status).toBe(TaskStatus.Running);
    });
  });
}
