import { AppConfig } from "@/lib/app-config";
import { DiskSvc } from "@/lib/domain/disk/svc";
import { ItemQuerySvc } from "@/lib/domain/item/query/svc";
import { ItemSvc } from "@/lib/domain/item/svc";
import { RestateClient } from "@/lib/infra/restate/client";
import { S3Client } from "@/lib/infra/s3/client";
import { server } from "@/mocks/server";
import { PrismaClient, Task } from "@prisma/client";
import { http, HttpResponse } from "msw";
import { describe, expect, it, vi } from "vitest";
import { mock } from "vitest-mock-extended";
import {
  FlipTaskReq,
  getTaskTmpDiskRootPath,
  replaceSuffix,
  RescaleIntensityTaskReq,
  SaveAsTaskReq,
  TaskRemoteInnerState,
  TaskType,
} from "./svc";

const REMOTE_TASK_API_URL = "https://localhost:2345";
const itemQuerySvc = mock<ItemQuerySvc>();
const itemSvc = mock<ItemSvc>();
const prisma = mock<PrismaClient>();
const appConfig = mock<AppConfig>({
  REMOTE_TASK_API_URL: REMOTE_TASK_API_URL,
});
const restateClient = mock<RestateClient>();
const s3Client = mock<S3Client>();
const diskSvc = mock<DiskSvc>();

const getSUT = async () => {
  vi.resetModules();
  const { TaskSvc } = await import("./svc");

  const svc = new TaskSvc(
    itemQuerySvc,
    itemSvc,
    prisma,
    appConfig,
    restateClient,
    s3Client,
    diskSvc
  );
  return svc;
};

describe("replaceSuffix", () => {
  it("should replace suffix", () => {
    expect(replaceSuffix("test.test.txt", "jpg")).toBe("test.test.jpg");
    expect(replaceSuffix("test", "jpg")).toBe("test.jpg");
    expect(replaceSuffix("test.txt", "jpg")).toBe("test.jpg");
  });

  it("should return object name", () => {
    expect(replaceSuffix("/folder/test", "jpg")).toBe("test.jpg");
  });
});

describe("_prepareBaseSaveAs", async () => {
  it("should prepare base save as when saveAsType is irs", async () => {
    const { createItem } = vi.hoisted(() => ({
      createItem: vi.fn(),
    }));

    vi.doMock("@/lib/domain/item/dbt", () => ({
      getItem: vi
        .fn()
        .mockResolvedValueOnce({
          iid: "2",
          piid_path: "/prjd-id/1/",
          name: "xxx.irs",
        })
        .mockResolvedValueOnce({
          iid: "3",
          piid_path: "/prjd-id/1/",
          name: "tgt-folder",
        }),
    }));

    vi.doMock("@/lib/domain/item/svc", async (importOriginal) => ({
      ...(await importOriginal()),
      createItem: createItem
        .mockResolvedValueOnce({
          iid: "4",
          piid_path: "/prjd-id/1/",
          name: "output-folder",
          id: "output-folder-id",
        })
        .mockResolvedValueOnce({
          iid: "5",
          piid_path: "/prjd-id/1/",
          name: "channel-1-xx.irs",
          id: "channel-1-file-id",
        })
        .mockResolvedValueOnce({
          iid: "6",
          piid_path: "/prjd-id/1/",
          name: "channel-2-xx.irs",
          id: "channel-2-file-id",
        }),
    }));

    const taskSvc = await getSUT();

    // @ts-expect-error: Accessing private property for test mocking
    taskSvc.itemQuerySvc.getTdbGroupUri = vi
      .fn()
      .mockResolvedValueOnce("s3://test-bkt/xxx.irs")
      .mockResolvedValueOnce("s3://test-bkt/channel-1-file.irs")
      .mockResolvedValueOnce("s3://test-bkt/channel-2-file.irs");

    const taskId = "uuid-task-id-kasgbaq1223a";
    const taskArgs: SaveAsTaskReq["taskArgs"] = {
      projectId: "prj-id",
      itemId: "item-id",
      setting: {
        channelChosenType: "shown",
        splitChannel: true,
        saveAsType: "irs",
        selectedChannels: [
          { idx: 0, name: "channel-name-1" },
          { idx: 1, name: "channel-name-1" },
        ],
        region: {
          x: 1463,
          y: 394,
          width: 802,
          height: 491,
        },
      },
      output: {
        folder_name: "folder-name",
        tgt_folder_id: "tgt-folder-id",
      },
    };
    const result = await taskSvc._prepareBaseSaveAs({
      taskId,
      taskArgs,
      operator: {
        id: "userId",
        isTestAccount: false,
      },
    });
    expect(result).toEqual({
      extra: {
        outputItemIds: ["channel-1-file-id", "channel-2-file-id"],
      },
      remoteReq: {
        task_type: "save_as",
        task_args: {
          req: {
            src_img_id: "s3://test-bkt/xxx.irs",
            tgt_type: "irs",
            channel_outputs: [
              {
                sub_path: "s3://test-bkt/channel-1-file.irs",
                channel_filters: [{ idx: 0 }],
              },
              {
                sub_path: "s3://test-bkt/channel-2-file.irs",
                channel_filters: [{ idx: 1 }],
              },
            ],
            xy_region: { x: 1463, y: 394, width: 802, height: 491 },
          },
        },
      },
    });
  });

  it("should prepare base save as when saveAsType is not irs", async () => {
    vi.doMock("@/lib/domain/item/dbt", () => ({
      getItem: vi.fn().mockResolvedValueOnce({
        iid: "2",
        piid_path: "/prjd-id/1/",
        name: "xxx.irs",
      }),
    }));

    const taskSvc = await getSUT();
    // @ts-expect-error: Accessing private property for test mocking
    taskSvc.itemQuerySvc.getTdbGroupUri = vi
      .fn()
      .mockResolvedValueOnce("s3://test-bkt/xxx.irs");
    const taskId = "uuid-task-id-kasgbaq1223a";
    const taskArgs: SaveAsTaskReq["taskArgs"] = {
      projectId: "prj-id",
      itemId: "item-id",
      setting: {
        channelChosenType: "shown",
        splitChannel: true,
        saveAsType: "jpg",
        selectedChannels: [
          { idx: 0, name: "channel-name-1" },
          { idx: 1, name: "channel-name-1" },
        ],
      },
      output: {
        folder_name: "folder-name",
        tgt_folder_id: "/dataSet/数据组1",
      },
    };

    const result = await taskSvc._prepareBaseSaveAs({
      taskId,
      taskArgs,
      operator: {
        id: "userId",
        isTestAccount: false,
      },
    });

    expect(result).toEqual({
      extra: {
        outputItemIds: [],
        outputsMoveFolderPath: "/dataSet/数据组1",
      },
      remoteReq: {
        task_type: "save_as",
        task_args: {
          req: {
            src_img_id: "s3://test-bkt/xxx.irs",
            tgt_type: "jpg",
            channel_outputs: [
              {
                sub_path: `project/task-tmp-outputs/prj-id/${taskId}/0-channel-name-1-folder-name.jpg`,
                channel_filters: [{ idx: 0 }],
              },
              {
                sub_path: `project/task-tmp-outputs/prj-id/${taskId}/1-channel-name-1-folder-name.jpg`,
                channel_filters: [{ idx: 1 }],
              },
            ],
            xy_region: undefined,
          },
        },
      },
    });
  });
});

describe("runFlip", async () => {
  it("should run flip", async () => {
    vi.doMock("@/lib/domain/item/dbt", () => ({
      getItem: vi.fn().mockResolvedValueOnce({
        iid: "2",
        piid_path: "/prjd-id/1/",
        name: "xxx.irs",
      }),
    }));

    const taskSvc = await getSUT();

    // @ts-expect-error: Accessing private property for test mocking
    taskSvc.itemQuerySvc.getTdbGroupUri = vi
      .fn()
      .mockResolvedValueOnce("s3://test-bkt/xxx.irs");

    const startRemoteTask = vi
      .spyOn(taskSvc, "startRemoteTask")
      .mockImplementation(vi.fn().mockResolvedValueOnce("remote-task-id"));
    vi.spyOn(taskSvc, "bindRemoteTaskAndUpdateStatus").mockImplementation(
      vi.fn()
    );

    const taskId = "uuid-task-id-kasgbaq1223a";
    const taskArgs: FlipTaskReq["taskArgs"] = {
      projectId: "prj-id",
      itemId: "item-id",
      setting: {
        channelChosenType: "shown",
        splitChannel: true,
        saveAsType: "jpg",
        selectedChannels: [
          { idx: 0, name: "channel-name-1" },
          { idx: 1, name: "channel-name-1" },
        ],
        flip_dims: "X",
      },
      output: {
        folder_name: "folder-name",
        tgt_folder_id: "/dataSet/数据组1",
      },
    };

    await taskSvc.runFlip({
      task: {
        id: taskId,
        task_type: TaskType.flip,
        task_args: taskArgs,
      } as unknown as Task,
      operator: { id: "userId", isTestAccount: false },
    });

    expect(startRemoteTask).toHaveBeenCalledWith({
      task_id: taskId,
      remote_task_req: {
        task_type: "save_as",
        task_args: {
          req: {
            src_img_id: "s3://test-bkt/xxx.irs",
            tgt_type: "jpg",
            channel_outputs: [
              {
                sub_path: `project/task-tmp-outputs/prj-id/${taskId}/0-channel-name-1-folder-name.jpg`,
                channel_filters: [{ idx: 0 }],
              },
              {
                sub_path: `project/task-tmp-outputs/prj-id/${taskId}/1-channel-name-1-folder-name.jpg`,
                channel_filters: [{ idx: 1 }],
              },
            ],
            flip_axes: { dims: "X" },
          },
        },
      },
    });
  });
});

describe("runRescaleIntensity", () => {
  it("should run rescale intensity", async () => {
    vi.doMock("@/lib/domain/item/dbt", () => ({
      getItem: vi.fn().mockResolvedValueOnce({
        iid: "2",
        piid_path: "/prjd-id/1/",
        name: "xxx.irs",
      }),
    }));

    const taskSvc = await getSUT();

    // @ts-expect-error: Accessing private property for test mocking
    taskSvc.itemQuerySvc.getTdbGroupUri = vi
      .fn()
      .mockResolvedValueOnce("s3://test-bkt/xxx.irs");

    const startRemoteTask = vi
      .spyOn(taskSvc, "startRemoteTask")
      .mockImplementation(vi.fn().mockResolvedValueOnce("remote-task-id"));
    vi.spyOn(taskSvc, "bindRemoteTaskAndUpdateStatus").mockImplementation(
      vi.fn()
    );

    const taskId = "uuid-task-id-kasgbaq1223a";
    const taskArgs: RescaleIntensityTaskReq["taskArgs"] = {
      projectId: "prj-id",
      itemId: "item-id",
      setting: {
        channelChosenType: "shown",
        splitChannel: true,
        saveAsType: "jpg",
        selectedChannels: [
          {
            idx: 0,
            name: "channel-name-1",
            rescale_range: { min: 0, max: 255 },
          },
          {
            idx: 1,
            name: "channel-name-1",
            rescale_range: { min: 0, max: 255 },
          },
        ],
      },
      output: {
        folder_name: "folder-name",
        tgt_folder_id: "/dataSet/数据组1",
      },
    };

    await taskSvc.runRescaleIntensity({
      task: {
        id: taskId,
        task_type: TaskType.rescale_intensity,
        task_args: taskArgs,
      } as unknown as Task,
      operator: { id: "userId", isTestAccount: false },
    });

    expect(startRemoteTask).toHaveBeenCalledWith({
      task_id: taskId,
      remote_task_req: {
        task_type: "save_as",
        task_args: {
          req: {
            src_img_id: "s3://test-bkt/xxx.irs",
            tgt_type: "jpg",
            channel_outputs: [
              {
                sub_path: `project/task-tmp-outputs/prj-id/${taskId}/0-channel-name-1-folder-name.jpg`,
                channel_filters: [{ idx: 0, rescale_range: [0, 255] }],
              },
              {
                sub_path: `project/task-tmp-outputs/prj-id/${taskId}/1-channel-name-1-folder-name.jpg`,
                channel_filters: [{ idx: 1, rescale_range: [0, 255] }],
              },
            ],
          },
        },
      },
    });
  });
});

describe("updateRemoteTaskStatus", () => {
  it("should return false when task is not found", async () => {
    vi.doMock("@/lib/domain/task/dbt", () => ({
      getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce(null),
    }));

    const taskSvc = await getSUT();
    const result = await taskSvc.updateRemoteTaskStatus({
      taskId: "task-id",
      remoteTaskId: "remote-task-id",
    });
    expect(result).toEqual({ keepPolling: false });
  });

  it("should not keep polling when remote task state is null & taskDurationInSecond > timeoutSecondForLostState", async () => {
    vi.doMock("dayjs", () => ({
      default: vi.fn(() => ({
        diff: vi.fn().mockReturnValue(32),
      })),
    }));

    const mockRecordTaskError = vi.fn();

    vi.doMock("@/lib/domain/task/dbt", () => ({
      getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
        id: "task-id",
        start_at: new Date("2025-01-01T01:23:00.000Z"),
      }),
      recordTaskError: mockRecordTaskError,
    }));
    const taskSvc = await getSUT();
    server.use(
      http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
        return HttpResponse.json(null);
      })
    );

    const result = await taskSvc.updateRemoteTaskStatus({
      taskId: "task-id",
      remoteTaskId: "remote-task-id",
    });

    expect(result).toEqual({ keepPolling: false });
    expect(mockRecordTaskError).toHaveBeenCalledWith(prisma, {
      task_id: "task-id",
      errorMsg: "计算服务重启，请重试任务",
    });
  });

  it("should  keep polling when remote task state is null & taskDurationInSecond <= timeoutSecondForLostState", async () => {
    vi.doMock("dayjs", () => ({
      default: vi.fn(() => ({
        diff: vi.fn().mockReturnValue(29),
      })),
    }));

    vi.doMock("@/lib/domain/task/dbt", () => ({
      getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
        id: "task-id",
        start_at: new Date("2025-01-01T01:23:00.000Z"),
      }),
    }));
    const taskSvc = await getSUT();
    server.use(
      http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
        return HttpResponse.json(null);
      })
    );

    const result = await taskSvc.updateRemoteTaskStatus({
      taskId: "task-id",
      remoteTaskId: "remote-task-id",
    });

    expect(result).toEqual({ keepPolling: true });
  });

  it("should record task error when remote task status is failed", async () => {
    const mockRecordTaskError = vi.fn();

    vi.doMock("@/lib/domain/task/dbt", () => ({
      getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
        id: "task-id",
      }),
      recordTaskError: mockRecordTaskError,
    }));

    const taskSvc = await getSUT();

    server.use(
      http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
        return HttpResponse.json({
          state: TaskRemoteInnerState.FAILED,
          error_message: "error_message",
        });
      })
    );

    const result = await taskSvc.updateRemoteTaskStatus({
      taskId: "task-id",
      remoteTaskId: "remote-task-id",
    });
    expect(result).toEqual({ keepPolling: false });
    expect(mockRecordTaskError).toHaveBeenCalledWith(prisma, {
      task_id: "task-id",
      errorMsg: "error_message",
      remote_task_status: TaskRemoteInnerState.FAILED,
    });
  });

  it("should not keep polling when remote task status is finished", async () => {
    vi.doMock("@/lib/domain/task/dbt", () => ({
      getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
        id: "task-id",
      }),
    }));

    const taskSvc = await getSUT();
    const mockProcessAfterTaskDone = vi.fn();
    // @ts-expect-error: Accessing private property for test mocking
    taskSvc.rsClient.taskSvc = {
      sendClient: {
        processAfterTaskDone: mockProcessAfterTaskDone,
      },
    };
    server.use(
      http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
        return HttpResponse.json({
          state: TaskRemoteInnerState.FINISHED,
        });
      })
    );

    const result = await taskSvc.updateRemoteTaskStatus({
      taskId: "task-id",
      remoteTaskId: "remote-task-id",
    });
    expect(result).toEqual({ keepPolling: false });
    expect(mockProcessAfterTaskDone).toHaveBeenCalledWith({
      taskId: "task-id",
    });
  });

  it("should keep polling & update remote task status when remote task status is running", async () => {
    const mockUpdateTaskRemoteStatus = vi.fn();
    vi.doMock("@/lib/domain/task/dbt", () => ({
      getTaskByRemoteTaskId: vi.fn().mockResolvedValueOnce({
        id: "task-id",
      }),
      updateTaskRemoteStatus: mockUpdateTaskRemoteStatus,
    }));
    const taskSvc = await getSUT();

    server.use(
      http.get(REMOTE_TASK_API_URL + `/task-state/remote-task-id`, () => {
        return HttpResponse.json({
          state: TaskRemoteInnerState.PENDING_NODE_ASSIGNMENT,
        });
      })
    );

    const result = await taskSvc.updateRemoteTaskStatus({
      taskId: "task-id",
      remoteTaskId: "remote-task-id",
    });
    expect(result).toEqual({ keepPolling: true });
    expect(mockUpdateTaskRemoteStatus).toHaveBeenCalledWith(prisma, {
      taskId: "task-id",
      remoteTaskStatus: TaskRemoteInnerState.PENDING_NODE_ASSIGNMENT,
    });
  });
});

describe("afterSaveAs", () => {
  it("should update item size", async () => {
    const taskSvc = await getSUT();
    const mockUpdateItemSize = vi.fn();
    // @ts-expect-error: Accessing private property for test mocking
    taskSvc.itemSvc.updateItemSize = mockUpdateItemSize;
    await taskSvc.afterSaveAs({
      task: {
        id: "task-id",
        project_id: "project-id",
        extra: {
          outputItemIds: ["item-id", "item-id-2"],
        },
      } as unknown as Task,
    });
    expect(mockUpdateItemSize).toHaveBeenCalledTimes(2);
    expect(mockUpdateItemSize).toHaveBeenCalledWith({
      itemId: "item-id",
    });
    expect(mockUpdateItemSize).toHaveBeenCalledWith({
      itemId: "item-id-2",
    });
  });

  it("should move temp outputs to target folder", async () => {
    const taskSvc = await getSUT();
    const mockMoveTo = vi.fn();
    // @ts-expect-error: Accessing private property for test mocking
    taskSvc.diskSvc.moveTo = mockMoveTo;
    await taskSvc.afterSaveAs({
      task: {
        id: "task-id",
        project_id: "project-id-5",
        extra: {
          outputItemIds: [],
          outputsMoveFolderPath: "/set/folder/",
        },
      } as unknown as Task,
    });

    expect(mockMoveTo).toHaveBeenCalledWith({
      projectId: "project-id-5",
      genSourceBaseRootPath: expect.any(Function),
      sourcePath: "task-id/",
      destinationPath: "/set/folder/task-id/",
    });

    const callArgs = mockMoveTo.mock.calls[0][0];
    const genSourceBaseRootPathFn = callArgs.genSourceBaseRootPath;
    expect(genSourceBaseRootPathFn()).toBe(
      getTaskTmpDiskRootPath({ projectId: "project-id-5" })
    );
  });
});

describe("getTaskTmpDiskRootPath", () => {
  it("should return the correct task running disk root path", () => {
    const rootPath = getTaskTmpDiskRootPath({ projectId: "project-id" });
    expect(rootPath).toBe("project/task-tmp-outputs/project-id");
  });
});
