import type {
  <PERSON><PERSON><PERSON><PERSON>,
  TaskRemoteInnerState,
  TaskReq,
} from "@/lib/domain/task/svc";
import { Operator } from "@/lib/domain/user/m";
import { Prisma, PrismaClient } from "@prisma/client";

export async function getTasksByPrjId(
  prisma: PrismaClient,
  {
    prjId,
    orderBy,
    length,
    remoteStatus,
    finishedAt,
  }: {
    prjId: string;
    orderBy?:
      | Prisma.TaskOrderByWithRelationInput
      | Prisma.TaskOrderByWithRelationInput[];
    length?: number;
    remoteStatus?: Prisma.TaskWhereInput["remote_task_status"];
    finishedAt?: Prisma.TaskWhereInput["finished_at"];
  }
) {
  const tasks = await prisma.task.findMany({
    where: {
      project_id: prjId,
      remote_task_status: remoteStatus,
      finished_at: finishedAt,
    },
    orderBy,
    take: length,
  });

  return tasks;
}

export async function resetTaskStateToStart(
  prisma: PrismaClient,
  {
    task_id,
    operator,
  }: {
    task_id: string;
    operator: Operator;
  }
) {
  await prisma.task.update({
    where: {
      id: task_id,
    },
    data: {
      created_by_id: operator.id,
      start_at: new Date(),
      remote_task_id: null,
      remote_task_status: null,
      finished_at: null,
      error: null,
    },
  });
}

export async function recordTaskError(
  prisma: PrismaClient,
  {
    task_id,
    errorMsg,
    remote_task_status,
  }: {
    task_id: string;
    errorMsg?: string;
    remote_task_status?: string;
  }
) {
  await prisma.task.update({
    where: {
      id: task_id,
    },
    data: {
      remote_task_status: remote_task_status,
      finished_at: new Date(),
      error: errorMsg,
    },
  });
}

export async function bindRemoteTask(
  prisma: PrismaClient,
  {
    task_id,
    remote_task_id,
    extra,
  }: {
    task_id: string;
    remote_task_id: string;
    extra?: TaskExtra;
  }
) {
  await prisma.task.update({
    where: {
      id: task_id,
    },
    data: {
      remote_task_id: remote_task_id,
      extra: extra,
    },
  });
}

export async function addTask(
  prisma: PrismaClient,
  {
    projectId,
    taskReq,
    operator,
    taskId,
  }: {
    projectId: string;
    taskReq: TaskReq;
    operator: Operator;
    taskId?: string;
  }
) {
  const { taskType, taskArgs } = taskReq;
  const task = await prisma.task.create({
    data: {
      id: taskId,
      project_id: projectId,
      task_type: taskType,
      task_args: taskArgs,
      created_by_id: operator.id,
    },
  });
  return task;
}

export async function updateTaskRemoteStatus(
  prisma: PrismaClient,
  {
    taskId,
    remoteTaskStatus,
  }: {
    taskId: string;
    remoteTaskStatus: TaskRemoteInnerState;
  }
) {
  await prisma.task.update({
    where: {
      id: taskId,
    },
    data: {
      remote_task_status: remoteTaskStatus,
    },
  });
}

export async function getTaskByRemoteTaskId(
  prisma: PrismaClient,
  {
    remoteTaskId,
    taskId,
  }: {
    remoteTaskId: string;
    taskId: string;
  }
) {
  const task = await prisma.task.findFirst({
    where: {
      id: taskId,
      remote_task_id: remoteTaskId,
    },
  });

  return task;
}
