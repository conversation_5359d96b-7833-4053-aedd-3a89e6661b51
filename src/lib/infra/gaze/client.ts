import type { AppConfig } from "@/lib/app-config";
import wretch, { type Wretch } from "wretch";
import querystring, { QueryStringAddon } from "wretch/addons/queryString";
import { WretchError } from "wretch/resolver";
import z, { ZodError } from "zod";

export type SaveImgMetaReq = {
  id: string;
  channels?:
    | {
        id: number;
        name?: string | null;
        color?: string | null;
        view_min?: number | null;
        view_max?: number | null;
        view_shown?: boolean | null;
      }[]
    | null;
  phys_x?: number | null;
  phys_x_unit?: string | null;
  phys_y?: number | null;
  phys_y_unit?: string | null;
  view_gamma?: number | null;
};

export type GetImgTileReq = {
  id: string;
  x: number;
  y: number;
  z: number;
  channel_id: number;
  version: string;
};

type GetMinMaxByQuantileReq = {
  id: string;
  channel_id: number;
  min_quantile?: number | null;
  max_quantile?: number | null;
  in_unique: boolean;
};

const GetImgInfoResSchema = z.object({
  id: z.string(),
  img_meta: z.object({
    tile_size: z.int(),
    base_x: z.int(),
    base_y: z.int(),
    min_level: z.int(),
    max_level: z.int(),
    dtype: z.string(),
    channels: z.array(
      z.object({
        id: z.int(),
        name: z.string(),
        min: z.number(),
        max: z.number(),
        color: z.string().nullish(),
        view_max: z.number().nullish(),
        view_min: z.number().nullish(),
        view_shown: z.boolean().nullish(),
      })
    ),
    axes: z.string(),
    shape: z.array(z.int()),
    phys_x: z.number().nullish(),
    phys_x_unit: z.string().nullish(),
    phys_y: z.number().nullish(),
    phys_y_unit: z.string().nullish(),
    phys_z: z.number().nullish(),
    phys_z_unit: z.string().nullish(),
    data_version: z.string(),
    view_gamma: z.number().nullish(),
  }),
});
type GetImgInfoRes = z.infer<typeof GetImgInfoResSchema>;

const GetMinMaxByQuantileResSchema = z.object({
  min: z.number().nullish(),
  max: z.number().nullish(),
});
type GetMinMaxByQuantileRes = z.infer<typeof GetMinMaxByQuantileResSchema>;

const GetHistogramResSchema = z.object({
  channels: z.array(
    z.object({
      id: z.number(),
      hist: z.array(z.number()),
      bins: z.array(z.number()),
    })
  ),
});
type GetHistogramRes = z.infer<typeof GetHistogramResSchema>;

export class GazeClient {
  private readonly remoteApi: Wretch<QueryStringAddon> & QueryStringAddon;
  constructor(appConfig: AppConfig) {
    this.remoteApi = wretch(appConfig.GAZE_API_URL).addon(querystring);
  }

  async getImgInfo({ tdbGroupUri }: { tdbGroupUri: string }) {
    const remoteRes = await this.remoteApi
      .query({ id: tdbGroupUri })
      .get("/api/img/meta")
      .json();
    const parsedRes = GetImgInfoResSchema.parse(remoteRes);
    return parsedRes;
  }

  async getImgOriginalMeta({ tdbGroupUri }: { tdbGroupUri: string }) {
    const remoteRes = await this.remoteApi
      .query({ id: tdbGroupUri })
      .get("/api/img/original-meta")
      .res();
    return remoteRes;
  }

  async saveImgMeta(options: SaveImgMetaReq) {
    await this.remoteApi.url("/api/img/meta").put(options).res();
  }

  getImgTile(options: GetImgTileReq) {
    return this.remoteApi.query(options).get("/api/img/tile").res();
  }

  async getMinMaxByQuantile(options: GetMinMaxByQuantileReq) {
    const remoteRes = await this.remoteApi
      .query(options)
      .get("/api/img/min-max-by-quantile")
      .json();
    const parsedRes = GetMinMaxByQuantileResSchema.parse(remoteRes);
    return parsedRes;
  }

  async getHistogram({ tdbGroupUri }: { tdbGroupUri: string }) {
    const remoteRes = await this.remoteApi
      .query({ id: tdbGroupUri })
      .get("/api/img/histogram")
      .json();
    const parsedRes = GetHistogramResSchema.parse(remoteRes);
    return parsedRes;
  }
}

if (import.meta.vitest) {
  const { describe, it, expect } = await import("vitest");
  const { http, HttpResponse } = await import("msw");
  const { server } = await import("@/mocks/server");

  const GAZE_API_URL = "http://localhost:8099";
  const client = new GazeClient({
    GAZE_API_URL,
  } as AppConfig);

  describe("getImgInfo", () => {
    const imgInfoRes: GetImgInfoRes = {
      id: "test-item-id",
      img_meta: {
        tile_size: 256,
        base_x: 1000,
        base_y: 1000,
        min_level: 0,
        max_level: 4,
        dtype: "uint8",
        channels: [],
        axes: "YX",
        shape: [1000, 1000],
        data_version: "1",
      },
    };

    it("should return img info", async () => {
      server.use(
        http.get(`${GAZE_API_URL}/api/img/meta`, () =>
          HttpResponse.json(imgInfoRes)
        )
      );

      const res = await client.getImgInfo({
        tdbGroupUri: "test-tdb-group-uri",
      });
      expect(res).toEqual(imgInfoRes);
    });

    it("should throw error when response structure is wrong", async () => {
      server.use(
        http.get(`${GAZE_API_URL}/api/img/meta`, () =>
          HttpResponse.json({ id: "11" })
        )
      );
      await expect(
        client.getImgInfo({ tdbGroupUri: "test-tdb-group-uri" })
      ).rejects.toThrowError(ZodError);
    });
  });

  describe("getImgOriginalMeta", () => {
    const imgOriginalMetaRes = {
      meta: {
        OME: {
          Creator: "OME Bio-Formats 8.1.1",
          Image: {
            ID: "Image:0",
            Name: "xx image",
            InstrumentRef: {
              ID: "Instrument:0",
            },
            Pixels: {
              BigEndian: false,
              ID: "Pixels:0",
              sizeC: 3,
            },
          },
          Instrument: {
            Detector: {
              Gain: 1,
            },
            ID: "Instrument:0",
            Objective: {
              ID: "Objective:0:0",
            },
          },
          UUID: "urn:uuid:2232sasgxzvaghaah-agas-ada13",
          "{http://www.w3.org/2001/XMLSchema-instance}schemaLocation":
            "http://www.openmicroscopy.org/Schemas/OME/2016-06 http://www.openmicroscopy.org/Schemas/OME/2016-06/ome.xsd",
        },
      },
    };

    it("should return img original data", async () => {
      server.use(
        http.get(`${GAZE_API_URL}/api/img/original-meta`, () =>
          HttpResponse.json(imgOriginalMetaRes)
        )
      );
      const res = await client.getImgOriginalMeta({
        tdbGroupUri: "test-tdb-group-uri",
      });
      expect(await res.json()).toEqual(imgOriginalMetaRes);
    });
  });

  describe("saveImgMeta", () => {
    it("should save img meta", async () => {
      server.use(
        http.put(`${GAZE_API_URL}/api/img/meta`, () => HttpResponse.json({}))
      );
      await expect(
        client.saveImgMeta({
          id: "test-item-id",
          channels: [],
          phys_x: 1,
          phys_x_unit: "mm",
        })
      ).resolves.not.toThrow();
    });

    it("should throw error when backend failed", async () => {
      server.use(
        http.put(
          `${GAZE_API_URL}/api/img/meta`,
          () => new HttpResponse(null, { status: 500 })
        )
      );
      await expect(
        client.saveImgMeta({
          id: "test-item-id",
        })
      ).rejects.toThrowError(WretchError);
    });
  });

  describe("getMinMaxByQuantile", () => {
    const getMinMaxByQuantileRes: GetMinMaxByQuantileRes = {
      min: 12,
      max: 98,
    };

    it("should return min and max", async () => {
      server.use(
        http.get(`${GAZE_API_URL}/api/img/min-max-by-quantile`, () =>
          HttpResponse.json(getMinMaxByQuantileRes)
        )
      );

      const res = await client.getMinMaxByQuantile({
        id: "test-tdb-group-uri",
        channel_id: 12,
        in_unique: true,
      });

      expect(res).toEqual(getMinMaxByQuantileRes);
    });

    it("should throw error when response structure is wrong", async () => {
      server.use(
        http.get(`${GAZE_API_URL}/api/img/min-max-by-quantile`, () =>
          HttpResponse.json({ min: "235" })
        )
      );
      await expect(
        client.getMinMaxByQuantile({
          id: "test-tdb-group-uri",
          channel_id: 12,
          in_unique: true,
        })
      ).rejects.toThrowError(ZodError);
    });
  });

  describe("getHistogram", () => {
    const getHistogramRes: GetHistogramRes = {
      channels: [
        {
          id: 12,
          hist: [1, 2, 3],
          bins: [1, 2, 3],
        },
      ],
    };

    it("should return histogram", async () => {
      server.use(
        http.get(`${GAZE_API_URL}/api/img/histogram`, () =>
          HttpResponse.json(getHistogramRes)
        )
      );
      const res = await client.getHistogram({
        tdbGroupUri: "test-tdb-group-uri",
      });
      expect(res).toEqual(getHistogramRes);
    });

    it("should throw error when response structure is wrong", async () => {
      server.use(
        http.get(`${GAZE_API_URL}/api/img/histogram`, () =>
          HttpResponse.json({ channels: null })
        )
      );
      await expect(
        client.getHistogram({ tdbGroupUri: "test-tdb-group-uri" })
      ).rejects.toThrowError(ZodError);
    });
  });
}
